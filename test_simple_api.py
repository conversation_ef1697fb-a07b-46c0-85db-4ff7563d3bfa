#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试
Simple API Test

测试基本的API功能是否正常工作。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    try:
        print("测试基本导入...")
        
        # 测试数据库模型导入
        from src.netanalysis.models import User, UploadedFile, AnalysisTask
        print("✅ 数据库模型导入成功")
        
        # 测试数据库配置导入
        from src.netanalysis.models import get_database_manager
        print("✅ 数据库配置导入成功")
        
        # 测试核心工具导入
        from src.netanalysis.core.utils import detect_file_format, calculate_file_hash
        print("✅ 核心工具导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_database_creation():
    """测试数据库创建"""
    try:
        print("\n测试数据库创建...")
        
        from src.netanalysis.models import get_database_manager
        
        # 使用内存数据库进行测试
        config = {"url": "sqlite:///:memory:"}
        db_manager = get_database_manager(config)
        
        # 创建表
        db_manager.create_tables()
        print("✅ 数据库表创建成功")
        
        # 健康检查
        health = db_manager.health_check()
        print(f"✅ 数据库健康检查: {health['status']}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_api_creation():
    """测试API应用创建"""
    try:
        print("\n测试API应用创建...")
        
        from src.netanalysis.web.api import create_app
        
        app = create_app({
            'title': '测试应用',
            'debug': True
        })
        
        print("✅ FastAPI应用创建成功")
        print(f"✅ 应用标题: {app.title}")
        
        return True
    except Exception as e:
        print(f"❌ API创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_health_endpoint():
    """测试健康检查端点"""
    try:
        print("\n测试健康检查端点...")
        
        from fastapi.testclient import TestClient
        from src.netanalysis.web.api import create_app
        
        app = create_app({'debug': True})
        client = TestClient(app)
        
        response = client.get("/health")
        print(f"✅ 健康检查响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康状态: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始API基础功能测试\n")
    
    tests = [
        test_basic_imports,
        test_database_creation,
        test_api_creation,
        test_health_endpoint,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("测试失败，继续下一个测试...")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
