#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API模块单元测试
API Module Unit Tests

测试FastAPI应用的各种端点和功能。
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 导入被测试的模块
from src.netanalysis.web.api import create_app
from src.netanalysis.models.database import Base
from src.netanalysis.models.database_config import DatabaseManager


class TestAPIEndpoints:
    """API端点测试类"""
    
    @pytest.fixture
    def test_db(self):
        """创建测试数据库"""
        # 使用内存SQLite数据库进行测试
        engine = create_engine("sqlite:///:memory:", connect_args={"check_same_thread": False})
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # 创建表
        Base.metadata.create_all(bind=engine)
        
        yield TestingSessionLocal
        
        # 清理
        Base.metadata.drop_all(bind=engine)
    
    @pytest.fixture
    def client(self, test_db):
        """创建测试客户端"""
        app = create_app({
            'title': '测试应用',
            'debug': True
        })
        
        # 覆盖数据库依赖
        def override_get_db():
            try:
                db = test_db()
                yield db
            finally:
                db.close()
        
        # 注入测试数据库
        from src.netanalysis.web.api import get_db
        app.dependency_overrides[get_db] = override_get_db
        
        return TestClient(app)
    
    def test_root_endpoint(self, client):
        """测试根路径端点"""
        response = client.get("/")
        assert response.status_code == 200
        assert "网络数据包分析工具" in response.text
    
    def test_health_check_endpoint(self, client):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "dependencies" in data
        
        # 检查关键依赖
        deps = data["dependencies"]
        assert "fastapi" in deps
        assert deps["fastapi"] is True
    
    def test_file_upload_invalid_format(self, client):
        """测试上传无效格式文件"""
        # 创建一个文本文件
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
            tmp_file.write(b"This is not a pcap file")
            tmp_file_path = tmp_file.name
        
        try:
            with open(tmp_file_path, "rb") as f:
                response = client.post(
                    "/api/v1/files/upload",
                    files={"file": ("test.txt", f, "text/plain")}
                )
            
            assert response.status_code == 400
            assert "不支持的文件格式" in response.json()["detail"]
        finally:
            os.unlink(tmp_file_path)
    
    def test_file_upload_too_large(self, client):
        """测试上传过大文件"""
        # 创建一个大文件（模拟）
        large_content = b"0" * (101 * 1024 * 1024)  # 101MB
        
        with tempfile.NamedTemporaryFile(suffix=".pcap", delete=False) as tmp_file:
            tmp_file.write(large_content[:1024])  # 只写入一小部分用于测试
            tmp_file_path = tmp_file.name
        
        try:
            # 模拟大文件上传
            with patch('src.netanalysis.web.api.UploadFile.read') as mock_read:
                mock_read.return_value = large_content
                
                with open(tmp_file_path, "rb") as f:
                    response = client.post(
                        "/api/v1/files/upload",
                        files={"file": ("large.pcap", f, "application/octet-stream")}
                    )
                
                assert response.status_code == 413
                assert "文件过大" in response.json()["detail"]
        finally:
            os.unlink(tmp_file_path)
    
    def test_file_upload_success(self, client):
        """测试成功上传文件"""
        # 创建一个小的PCAP文件（模拟）
        pcap_content = b"\xd4\xc3\xb2\xa1\x02\x00\x04\x00"  # PCAP文件头的一部分
        
        with tempfile.NamedTemporaryFile(suffix=".pcap", delete=False) as tmp_file:
            tmp_file.write(pcap_content)
            tmp_file_path = tmp_file.name
        
        try:
            with open(tmp_file_path, "rb") as f:
                response = client.post(
                    "/api/v1/files/upload",
                    files={"file": ("test.pcap", f, "application/octet-stream")},
                    data={"description": "测试文件"}
                )
            
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "file_id" in data
            assert data["filename"]
            assert data["size"] == len(pcap_content)
            assert "upload_time" in data
        finally:
            os.unlink(tmp_file_path)
    
    def test_create_analysis_task_missing_fields(self, client):
        """测试创建分析任务缺少必需字段"""
        response = client.post(
            "/api/v1/analysis/create",
            json={"description": "测试任务"}  # 缺少name和uploaded_file_id
        )
        
        assert response.status_code == 400
        assert "缺少必需字段" in response.json()["detail"]
    
    def test_create_analysis_task_invalid_file(self, client):
        """测试创建分析任务使用无效文件ID"""
        response = client.post(
            "/api/v1/analysis/create",
            json={
                "name": "测试任务",
                "uploaded_file_id": "invalid-file-id",
                "description": "测试描述"
            }
        )
        
        assert response.status_code == 404
        assert "指定的文件不存在" in response.json()["detail"]
    
    def test_get_nonexistent_analysis_task(self, client):
        """测试获取不存在的分析任务"""
        response = client.get("/api/v1/analysis/nonexistent-task-id")
        
        assert response.status_code == 404
        assert "分析任务不存在" in response.json()["detail"]
    
    def test_list_analysis_tasks_empty(self, client):
        """测试获取空的分析任务列表"""
        response = client.get("/api/v1/analysis")
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["tasks"] == []
        assert data["total"] == 0
        assert data["skip"] == 0
        assert data["limit"] == 20
    
    def test_list_analysis_tasks_with_invalid_status(self, client):
        """测试使用无效状态过滤分析任务"""
        response = client.get("/api/v1/analysis?status=invalid_status")
        
        assert response.status_code == 400
        assert "无效的状态值" in response.json()["detail"]
    
    def test_list_analysis_tasks_pagination(self, client):
        """测试分析任务列表分页"""
        response = client.get("/api/v1/analysis?skip=10&limit=5")
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["skip"] == 10
        assert data["limit"] == 5


class TestAPIIntegration:
    """API集成测试类"""
    
    @pytest.fixture
    def app_with_db(self):
        """创建带有真实数据库的应用"""
        # 使用临时SQLite文件
        with tempfile.NamedTemporaryFile(suffix=".db", delete=False) as tmp_db:
            db_url = f"sqlite:///{tmp_db.name}"
        
        # 创建数据库管理器
        db_manager = DatabaseManager({"url": db_url})
        db_manager.create_tables()
        
        # 创建应用
        app = create_app({
            'title': '集成测试应用',
            'debug': True
        })
        
        yield app, db_manager
        
        # 清理
        db_manager.close()
        os.unlink(tmp_db.name)
    
    def test_full_workflow(self, app_with_db):
        """测试完整的工作流程：上传文件 -> 创建任务 -> 查询任务"""
        app, db_manager = app_with_db
        client = TestClient(app)
        
        # 1. 上传文件
        pcap_content = b"\xd4\xc3\xb2\xa1\x02\x00\x04\x00" * 100  # 模拟PCAP内容
        
        with tempfile.NamedTemporaryFile(suffix=".pcap", delete=False) as tmp_file:
            tmp_file.write(pcap_content)
            tmp_file_path = tmp_file.name
        
        try:
            # 上传文件
            with open(tmp_file_path, "rb") as f:
                upload_response = client.post(
                    "/api/v1/files/upload",
                    files={"file": ("workflow_test.pcap", f, "application/octet-stream")},
                    data={"description": "工作流程测试文件"}
                )
            
            assert upload_response.status_code == 200
            upload_data = upload_response.json()
            file_id = upload_data["file_id"]
            
            # 2. 创建分析任务
            task_response = client.post(
                "/api/v1/analysis/create",
                json={
                    "name": "工作流程测试任务",
                    "uploaded_file_id": file_id,
                    "description": "测试完整工作流程",
                    "enable_ai_analysis": True,
                    "enable_visualization": True,
                    "enable_anomaly_detection": True
                }
            )
            
            assert task_response.status_code == 200
            task_data = task_response.json()
            task_id = task_data["task_id"]
            
            # 3. 查询任务详情
            detail_response = client.get(f"/api/v1/analysis/{task_id}")
            
            assert detail_response.status_code == 200
            detail_data = detail_response.json()
            
            task_info = detail_data["task"]
            assert task_info["id"] == task_id
            assert task_info["name"] == "工作流程测试任务"
            assert task_info["uploaded_file_id"] == file_id
            assert task_info["enable_ai_analysis"] is True
            
            # 4. 查询任务列表
            list_response = client.get("/api/v1/analysis")
            
            assert list_response.status_code == 200
            list_data = list_response.json()
            
            assert list_data["total"] >= 1
            assert any(task["id"] == task_id for task in list_data["tasks"])
            
        finally:
            os.unlink(tmp_file_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
