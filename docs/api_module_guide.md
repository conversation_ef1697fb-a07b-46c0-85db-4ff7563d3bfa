# API模块使用指南

## 概述

网络数据包分析工具的API模块提供了完整的RESTful API接口，支持文件上传、分析任务管理、健康检查等核心功能。API基于FastAPI框架构建，具有自动文档生成、数据验证、异步处理等特性。

## 主要功能

### 1. 文件上传管理
- 支持PCAP、PCAPNG、CAP等网络包格式
- 文件大小限制和格式验证
- 文件去重和哈希校验
- 数据库存储文件元数据

### 2. 分析任务管理
- 创建、查询、列表分析任务
- 任务状态跟踪和进度监控
- 支持AI分析、可视化、异常检测配置
- 任务结果存储和检索

### 3. 健康检查和监控
- 系统健康状态检查
- 数据库连接监控
- 磁盘空间和内存使用监控
- 依赖服务状态检查

### 4. 中间件功能
- 请求日志记录
- 错误处理和异常捕获
- 性能监控和统计
- 安全防护和频率限制

## API端点详解

### 基础端点

#### GET /
- **功能**: 返回欢迎页面
- **响应**: HTML页面，包含系统介绍和导航链接

#### GET /health
- **功能**: 系统健康检查
- **响应**: JSON格式的健康状态报告
- **示例响应**:
```json
{
  "status": "healthy",
  "timestamp": "2025-08-27T12:00:00",
  "version": "1.0.0",
  "dependencies": {
    "fastapi": true,
    "database": true,
    "disk_space": true,
    "memory": true
  }
}
```

### 文件上传端点

#### POST /api/v1/files/upload
- **功能**: 上传网络数据包文件
- **请求**: multipart/form-data
  - `file`: 文件内容（必需）
  - `description`: 文件描述（可选）
- **支持格式**: .pcap, .pcapng, .cap, .dmp
- **文件大小限制**: 100MB
- **响应示例**:
```json
{
  "success": true,
  "file_id": "uuid-string",
  "filename": "timestamp_uuid_original.pcap",
  "original_filename": "capture.pcap",
  "size": 1024000,
  "format": "pcap",
  "hash": "sha256-hash",
  "upload_time": "2025-08-27T12:00:00",
  "message": "文件上传成功"
}
```

#### POST /upload (兼容性端点)
- **功能**: 简化的文件上传接口
- **用途**: 向后兼容，不使用数据库存储

### 分析任务端点

#### POST /api/v1/analysis/create
- **功能**: 创建新的分析任务
- **请求体**:
```json
{
  "name": "任务名称",
  "uploaded_file_id": "文件ID",
  "description": "任务描述",
  "enable_ai_analysis": true,
  "enable_visualization": true,
  "enable_anomaly_detection": true,
  "analysis_config": {}
}
```
- **响应示例**:
```json
{
  "success": true,
  "task_id": "task-uuid",
  "name": "任务名称",
  "status": "pending",
  "created_at": "2025-08-27T12:00:00",
  "message": "分析任务创建成功"
}
```

#### GET /api/v1/analysis/{task_id}
- **功能**: 获取分析任务详情
- **路径参数**: task_id - 任务ID
- **响应示例**:
```json
{
  "success": true,
  "task": {
    "id": "task-uuid",
    "name": "任务名称",
    "status": "completed",
    "progress": 100.0,
    "uploaded_file_id": "file-uuid",
    "enable_ai_analysis": true,
    "created_at": "2025-08-27T12:00:00",
    "completed_at": "2025-08-27T12:05:00",
    "processing_time": 300.5,
    "memory_usage": 512,
    "result_data": {}
  }
}
```

#### GET /api/v1/analysis
- **功能**: 获取分析任务列表
- **查询参数**:
  - `skip`: 跳过记录数（默认0）
  - `limit`: 返回记录数（默认20）
  - `status`: 按状态过滤（可选）
- **响应示例**:
```json
{
  "success": true,
  "tasks": [
    {
      "id": "task-uuid",
      "name": "任务名称",
      "status": "completed",
      "progress": 100.0,
      "uploaded_file_id": "file-uuid",
      "created_at": "2025-08-27T12:00:00",
      "completed_at": "2025-08-27T12:05:00"
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 20
}
```

## 数据模型

### 任务状态枚举
- `pending`: 等待中
- `running`: 运行中
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

### 文件格式枚举
- `pcap`: PCAP格式
- `pcapng`: PCAPNG格式
- `cap`: CAP格式
- `tcpdump`: Tcpdump格式
- `unknown`: 未知格式

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `404`: 资源不存在
- `413`: 文件过大
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

## 使用示例

### Python客户端示例
```python
import requests

# 上传文件
with open('capture.pcap', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/v1/files/upload',
        files={'file': f},
        data={'description': '网络捕获文件'}
    )
    file_data = response.json()
    file_id = file_data['file_id']

# 创建分析任务
task_response = requests.post(
    'http://localhost:8000/api/v1/analysis/create',
    json={
        'name': '网络流量分析',
        'uploaded_file_id': file_id,
        'enable_ai_analysis': True
    }
)
task_data = task_response.json()
task_id = task_data['task_id']

# 查询任务状态
status_response = requests.get(
    f'http://localhost:8000/api/v1/analysis/{task_id}'
)
status_data = status_response.json()
print(f"任务状态: {status_data['task']['status']}")
```

### curl命令示例
```bash
# 健康检查
curl http://localhost:8000/health

# 上传文件
curl -X POST \
  -F "file=@capture.pcap" \
  -F "description=测试文件" \
  http://localhost:8000/api/v1/files/upload

# 创建分析任务
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"name":"测试任务","uploaded_file_id":"file-uuid"}' \
  http://localhost:8000/api/v1/analysis/create

# 查询任务列表
curl "http://localhost:8000/api/v1/analysis?limit=10&status=completed"
```

## 配置说明

### 环境变量
- `DATABASE_URL`: 数据库连接URL
- `MAX_UPLOAD_SIZE`: 最大上传文件大小（字节）
- `UPLOAD_DIR`: 文件上传目录路径

### 应用配置
```python
app_config = {
    'title': '网络数据包分析工具',
    'description': 'API文档',
    'version': '1.0.0',
    'debug': False
}
```

## 部署指南

### 开发环境
```bash
# 启动开发服务器
uvicorn src.netanalysis.web.api:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境
```bash
# 使用Gunicorn启动
gunicorn src.netanalysis.web.api:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker部署
```dockerfile
FROM python:3.11-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
CMD ["uvicorn", "src.netanalysis.web.api:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 安全考虑

1. **文件上传安全**
   - 文件类型验证
   - 文件大小限制
   - 文件内容扫描

2. **API安全**
   - 请求频率限制
   - 输入数据验证
   - 错误信息过滤

3. **数据库安全**
   - SQL注入防护
   - 连接池管理
   - 事务处理

## 监控和日志

### 日志级别
- `INFO`: 正常操作日志
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `DEBUG`: 调试信息

### 监控指标
- 请求响应时间
- 错误率统计
- 内存使用情况
- 数据库连接状态

## 故障排除

### 常见问题
1. **文件上传失败**: 检查文件格式和大小
2. **数据库连接错误**: 验证数据库配置
3. **任务创建失败**: 确认文件ID有效性
4. **API响应慢**: 检查数据库性能和网络状况

### 调试技巧
1. 启用调试模式查看详细错误信息
2. 检查日志文件定位问题
3. 使用健康检查端点验证系统状态
4. 监控资源使用情况
