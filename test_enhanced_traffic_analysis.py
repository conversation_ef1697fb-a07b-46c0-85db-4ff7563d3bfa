#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强流量统计分析测试脚本
Enhanced Traffic Statistics Analysis Test Script

测试新实现的增强流量统计分析功能，包括：
- 基础流量统计计算
- 时间序列数据生成
- TCP连接状态分析
- UDP流分析
- 地理位置分析
- Top N统计
- 流量模式识别
- 质量指标计算
"""

import sys
import os
import json
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from netanalysis.core.models import Packet, ProtocolType, PacketDirection
from netanalysis.analyzers.traffic_analyzer import EnhancedTrafficAnalyzer


def create_test_packets() -> List[Packet]:
    """
    创建测试数据包
    
    生成包含不同协议、方向和时间分布的测试数据包，
    用于验证流量统计分析功能。
    
    Returns:
        List[Packet]: 测试数据包列表
    """
    packets = []
    base_time = datetime.now()
    
    # 创建TCP数据包（Web流量）
    for i in range(100):
        timestamp = base_time + timedelta(seconds=i * 0.1)
        
        # HTTP请求包
        packet = Packet(
            timestamp=timestamp,
            size=200 + i % 100,
            src_ip="*************",
            dst_ip="************",
            src_port=50000 + i,
            dst_port=80,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.OUTBOUND
        )
        packets.append(packet)
        
        # HTTP响应包
        response_packet = Packet(
            timestamp=timestamp + timedelta(milliseconds=50),
            size=1500 + i % 500,
            src_ip="************",
            dst_ip="*************",
            src_port=80,
            dst_port=50000 + i,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.INBOUND
        )
        packets.append(response_packet)
    
    # 创建UDP数据包（DNS查询）
    for i in range(50):
        timestamp = base_time + timedelta(seconds=i * 0.2)
        
        # DNS查询包
        query_packet = Packet(
            timestamp=timestamp,
            size=64,
            src_ip="*************",
            dst_ip="*******",
            src_port=53000 + i,
            dst_port=53,
            protocol=ProtocolType.UDP,
            direction=PacketDirection.OUTBOUND
        )
        packets.append(query_packet)
        
        # DNS响应包
        response_packet = Packet(
            timestamp=timestamp + timedelta(milliseconds=20),
            size=128,
            src_ip="*******",
            dst_ip="*************",
            src_port=53,
            dst_port=53000 + i,
            protocol=ProtocolType.UDP,
            direction=PacketDirection.INBOUND
        )
        packets.append(response_packet)
    
    # 创建内部网络流量
    for i in range(30):
        timestamp = base_time + timedelta(seconds=i * 0.3)
        
        packet = Packet(
            timestamp=timestamp,
            size=500 + i % 200,
            src_ip="*************",
            dst_ip="*************",
            src_port=8000,
            dst_port=8001,
            protocol=ProtocolType.TCP,
            direction=PacketDirection.INTERNAL
        )
        packets.append(packet)
    
    return packets


def test_enhanced_traffic_analysis():
    """
    测试增强流量统计分析功能
    """
    print("🚀 开始测试增强流量统计分析功能")
    print("=" * 60)
    
    # 创建测试数据
    print("📦 创建测试数据包...")
    packets = create_test_packets()
    print(f"✅ 创建了 {len(packets)} 个测试数据包")
    
    # 初始化分析器
    print("\n🔧 初始化增强流量分析器...")
    config = {
        'top_n_limit': 5,
        'time_window_seconds': 1,
        'enable_geo_analysis': True,
        'enable_quality_metrics': True
    }
    analyzer = EnhancedTrafficAnalyzer(config)
    print("✅ 分析器初始化完成")
    
    # 执行全面流量分析
    print("\n📊 执行全面流量统计分析...")
    try:
        traffic_stats = analyzer.analyze_traffic_comprehensive(packets)
        print("✅ 流量分析完成")
        
        # 显示分析结果
        print("\n" + "=" * 60)
        print("📈 流量统计分析结果")
        print("=" * 60)
        
        # 基础统计
        print(f"\n📋 基础统计:")
        print(f"  总数据包数: {traffic_stats.total_packets}")
        print(f"  总字节数: {traffic_stats.total_bytes:,}")
        print(f"  平均包大小: {traffic_stats.average_packet_size:.1f} 字节")
        print(f"  最小包大小: {traffic_stats.min_packet_size} 字节")
        print(f"  最大包大小: {traffic_stats.max_packet_size} 字节")
        print(f"  包大小方差: {traffic_stats.packet_size_variance:.2f}")
        
        # 速率统计
        print(f"\n⚡ 速率统计:")
        print(f"  包速率: {traffic_stats.packets_per_second:.2f} 包/秒")
        print(f"  字节速率: {traffic_stats.bytes_per_second:.2f} 字节/秒")
        print(f"  比特率: {traffic_stats.bits_per_second:.2f} bps")
        print(f"  带宽: {traffic_stats.bits_per_second/1000000:.2f} Mbps")
        
        # 连接统计
        print(f"\n🔗 连接统计:")
        print(f"  总连接数: {traffic_stats.total_connections}")
        print(f"  TCP连接数: {traffic_stats.tcp_connections}")
        print(f"  UDP流数: {traffic_stats.udp_flows}")
        print(f"  活跃连接数: {traffic_stats.active_connections}")
        print(f"  连接成功率: {traffic_stats.connection_success_rate:.2%}")
        print(f"  平均连接持续时间: {traffic_stats.average_connection_duration:.2f} 秒")
        
        # 方向统计
        print(f"\n🔄 方向统计:")
        print(f"  入站数据包: {traffic_stats.inbound_packets}")
        print(f"  出站数据包: {traffic_stats.outbound_packets}")
        print(f"  内部数据包: {traffic_stats.internal_packets}")
        print(f"  入站字节数: {traffic_stats.inbound_bytes:,}")
        print(f"  出站字节数: {traffic_stats.outbound_bytes:,}")
        print(f"  内部字节数: {traffic_stats.internal_bytes:,}")
        
        # 时间序列分析
        if traffic_stats.time_series_data:
            ts_data = traffic_stats.time_series_data
            print(f"\n📈 时间序列分析:")
            print(f"  时间窗口数: {ts_data.get('total_windows', 0)}")
            print(f"  窗口大小: {ts_data.get('window_size_seconds', 0)} 秒")
            
            # 显示统计信息
            packet_stats = ts_data.get('statistics', {}).get('packet_statistics', {})
            if packet_stats:
                print(f"  包数量统计:")
                print(f"    平均值: {packet_stats.get('mean', 0):.1f}")
                print(f"    中位数: {packet_stats.get('median', 0):.1f}")
                print(f"    标准差: {packet_stats.get('std_dev', 0):.1f}")
                print(f"    变异系数: {packet_stats.get('coefficient_of_variation', 0):.3f}")
            
            # 峰值检测
            peak_detection = ts_data.get('statistics', {}).get('peak_detection', {})
            if peak_detection:
                print(f"  峰值检测:")
                print(f"    包数量峰值: {peak_detection.get('packet_peak_count', 0)} 个")
                print(f"    字节数峰值: {peak_detection.get('byte_peak_count', 0)} 个")
            
            # 趋势分析
            trend_analysis = ts_data.get('statistics', {}).get('trend_analysis', {})
            if trend_analysis:
                print(f"  趋势分析:")
                print(f"    整体趋势: {trend_analysis.get('overall_trend', 'unknown')}")
                print(f"    包数量趋势: {trend_analysis.get('packet_trend', 'unknown')}")
                print(f"    字节数趋势: {trend_analysis.get('byte_trend', 'unknown')}")
        
        # 地理分布
        if traffic_stats.geo_distribution:
            print(f"\n🌍 地理分布:")
            for category, count in traffic_stats.geo_distribution.items():
                print(f"  {category}: {count}")
        
        # Top N统计
        if traffic_stats.top_n_statistics:
            top_n = traffic_stats.top_n_statistics
            print(f"\n🏆 Top N统计:")
            
            # Top源IP
            if 'top_source_ips' in top_n:
                print(f"  Top源IP地址:")
                for item in top_n['top_source_ips'][:3]:
                    print(f"    {item['ip']}: {item['packets']} 包 ({item['percentage']:.1f}%)")
            
            # Top协议
            if 'top_protocols' in top_n:
                print(f"  Top协议:")
                for item in top_n['top_protocols'][:3]:
                    print(f"    {item['protocol']}: {item['packets']} 包 ({item['percentage']:.1f}%)")
        
        # 流量模式
        if traffic_stats.traffic_patterns:
            patterns = traffic_stats.traffic_patterns
            print(f"\n🔍 流量模式识别:")
            
            traffic_type = patterns.get('traffic_type', {})
            if traffic_type:
                print(f"  流量类型:")
                print(f"    突发性: {traffic_type.get('burstiness', 'unknown')}")
                print(f"    流量规模: {traffic_type.get('volume', 'unknown')}")
                print(f"    协议主导: {traffic_type.get('protocol_dominance', 'unknown')}")
            
            network_behavior = patterns.get('network_behavior', {})
            if network_behavior:
                print(f"  网络行为:")
                print(f"    连接多样性: {network_behavior.get('connection_diversity', 0):.3f}")
                print(f"    方向偏向: {network_behavior.get('direction_bias', 'unknown')}")
        
        # 质量指标
        if traffic_stats.quality_metrics:
            quality = traffic_stats.quality_metrics
            print(f"\n⭐ 质量指标:")
            print(f"  效率评分: {quality.get('efficiency_score', 0):.1f}/100")
            print(f"  协议平衡评分: {quality.get('protocol_balance_score', 0):.1f}/100")
            print(f"  时间一致性评分: {quality.get('time_consistency_score', 0):.1f}/100")
            print(f"  综合质量评分: {quality.get('overall_quality_score', 0):.1f}/100")
        
        print("\n" + "=" * 60)
        print("✅ 增强流量统计分析测试完成！")
        
        # 保存详细结果到文件
        result_file = "enhanced_traffic_analysis_result.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            # 转换为可序列化的格式
            result_dict = {
                'basic_stats': {
                    'total_packets': traffic_stats.total_packets,
                    'total_bytes': traffic_stats.total_bytes,
                    'average_packet_size': traffic_stats.average_packet_size,
                    'packets_per_second': traffic_stats.packets_per_second,
                    'bits_per_second': traffic_stats.bits_per_second
                },
                'time_series_data': traffic_stats.time_series_data,
                'geo_distribution': traffic_stats.geo_distribution,
                'top_n_statistics': traffic_stats.top_n_statistics,
                'traffic_patterns': traffic_stats.traffic_patterns,
                'quality_metrics': traffic_stats.quality_metrics
            }
            json.dump(result_dict, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 详细结果已保存到: {result_file}")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_enhanced_traffic_analysis()
