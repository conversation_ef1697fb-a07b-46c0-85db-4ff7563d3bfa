"""初始数据库架构

Revision ID: db61633d2ec5
Revises: 
Create Date: 2025-08-27 03:53:10.649828

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'db61633d2ec5'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库架构"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.UUID(as_uuid=False), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=True),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('is_verified', sa.<PERSON>(), nullable=False),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('api_key', sa.String(length=64), nullable=True),
    sa.Column('api_key_created_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_api_key'), ['api_key'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    op.create_table('uploaded_files',
    sa.Column('id', sa.UUID(as_uuid=False), nullable=False),
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('file_hash', sa.String(length=64), nullable=False),
    sa.Column('file_format', sa.String(length=20), nullable=False),
    sa.Column('mime_type', sa.String(length=100), nullable=False),
    sa.Column('packet_count', sa.Integer(), nullable=True),
    sa.Column('capture_start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('capture_end_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('capture_duration', sa.Float(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('uploaded_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('user_id', sa.UUID(as_uuid=False), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('uploaded_files', schema=None) as batch_op:
        batch_op.create_index('idx_uploaded_files_hash', ['file_hash'], unique=False)
        batch_op.create_index('idx_uploaded_files_user_uploaded', ['user_id', 'uploaded_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_uploaded_files_file_hash'), ['file_hash'], unique=False)

    op.create_table('analysis_tasks',
    sa.Column('id', sa.UUID(as_uuid=False), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('progress', sa.Float(), nullable=False),
    sa.Column('analysis_config', sa.JSON(), nullable=False),
    sa.Column('enable_ai_analysis', sa.Boolean(), nullable=False),
    sa.Column('enable_visualization', sa.Boolean(), nullable=False),
    sa.Column('enable_anomaly_detection', sa.Boolean(), nullable=False),
    sa.Column('result_data', sa.JSON(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('result_file_path', sa.String(length=500), nullable=True),
    sa.Column('processing_time', sa.Float(), nullable=True),
    sa.Column('memory_usage', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('user_id', sa.UUID(as_uuid=False), nullable=False),
    sa.Column('uploaded_file_id', sa.UUID(as_uuid=False), nullable=False),
    sa.ForeignKeyConstraint(['uploaded_file_id'], ['uploaded_files.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('analysis_tasks', schema=None) as batch_op:
        batch_op.create_index('idx_analysis_tasks_file', ['uploaded_file_id'], unique=False)
        batch_op.create_index('idx_analysis_tasks_status', ['status'], unique=False)
        batch_op.create_index('idx_analysis_tasks_user_created', ['user_id', 'created_at'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """降级数据库架构"""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('analysis_tasks', schema=None) as batch_op:
        batch_op.drop_index('idx_analysis_tasks_user_created')
        batch_op.drop_index('idx_analysis_tasks_status')
        batch_op.drop_index('idx_analysis_tasks_file')

    op.drop_table('analysis_tasks')
    with op.batch_alter_table('uploaded_files', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_uploaded_files_file_hash'))
        batch_op.drop_index('idx_uploaded_files_user_uploaded')
        batch_op.drop_index('idx_uploaded_files_hash')

    op.drop_table('uploaded_files')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_api_key'))

    op.drop_table('users')
    # ### end Alembic commands ###
