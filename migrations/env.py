#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Alembic 环境配置
Alembic Environment Configuration

配置 Alembic 数据库迁移环境，支持在线和离线模式。
"""

import os
import sys
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入模型
from src.netanalysis.models.database import Base
from src.netanalysis.models.database_config import get_database_manager

# Alembic Config 对象，提供对 .ini 文件中值的访问
config = context.config

# 解释配置文件以进行 Python 日志记录
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 添加模型的 MetaData 对象以支持 'autogenerate'
target_metadata = Base.metadata


def get_database_url():
    """
    获取数据库连接URL
    优先级：环境变量 > 配置文件
    """
    # 从环境变量获取
    url = os.getenv('DATABASE_URL')
    if url:
        return url
    
    # 从数据库管理器获取
    try:
        db_manager = get_database_manager()
        return db_manager.config.database_url
    except Exception:
        # 默认使用 SQLite
        return "sqlite:///./data/netanalysis.db"


def run_migrations_offline() -> None:
    """
    在 'offline' 模式下运行迁移
    
    这将配置上下文仅使用 URL 而不是 Engine，
    尽管这里也需要一个 Engine，但我们不需要创建连接；
    只需要将 URL 传递给上下文即可。
    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
        render_as_batch=True,  # 支持 SQLite 的 ALTER TABLE
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """
    在 'online' 模式下运行迁移
    
    在这种情况下，我们需要创建一个 Engine 并将连接与上下文关联。
    """
    # 获取数据库URL并设置到配置中
    database_url = get_database_url()
    config.set_main_option("sqlalchemy.url", database_url)
    
    # 创建引擎配置
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = database_url
    
    # 为 SQLite 添加特殊配置
    if database_url.startswith("sqlite"):
        configuration["sqlalchemy.connect_args"] = {"check_same_thread": False}
    
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
            render_as_batch=True,  # 支持 SQLite 的 ALTER TABLE
        )

        with context.begin_transaction():
            context.run_migrations()


def include_name(name, type_, parent_names):
    """
    决定是否在迁移中包含特定的数据库对象
    
    Args:
        name: 对象名称
        type_: 对象类型 ('table', 'column', 'index', 'unique_constraint', 等)
        parent_names: 父对象名称字典
        
    Returns:
        bool: 是否包含此对象
    """
    # 排除某些系统表或临时表
    if type_ == "table":
        # 排除 Alembic 版本表
        if name == "alembic_version":
            return False
        # 排除以 temp_ 开头的临时表
        if name.startswith("temp_"):
            return False
    
    return True


def include_object(object, name, type_, reflected, compare_to):
    """
    决定是否在迁移中包含特定的数据库对象（更详细的控制）
    
    Args:
        object: 数据库对象
        name: 对象名称
        type_: 对象类型
        reflected: 是否从数据库反射得到
        compare_to: 比较的目标对象
        
    Returns:
        bool: 是否包含此对象
    """
    # 可以在这里添加更复杂的逻辑
    return include_name(name, type_, {})


# 根据上下文运行相应的迁移模式
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
