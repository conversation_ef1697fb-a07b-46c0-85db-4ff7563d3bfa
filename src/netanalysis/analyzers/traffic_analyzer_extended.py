#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量分析器扩展模块
Traffic Analyzer Extended Module

包含流量分析器的扩展方法，包括：
- UDP流详细分析
- 地理位置分析
- Top N统计生成
- 流量模式识别
- 质量指标计算

这些方法是EnhancedTrafficAnalyzer的扩展部分。
"""

import logging
import statistics
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import ipaddress

from ..core.models import Packet, ProtocolType, PacketDirection

logger = logging.getLogger(__name__)


class TrafficAnalyzerExtensions:
    """
    流量分析器扩展方法类
    
    包含UDP流分析、地理位置分析、Top N统计等扩展功能。
    """

    def __init__(self, top_n_limit: int = 10):
        """
        初始化扩展方法类
        
        Args:
            top_n_limit: Top N统计的数量限制
        """
        self.top_n_limit = top_n_limit
        self.logger = logging.getLogger(f"{__name__}.TrafficAnalyzerExtensions")

    def analyze_udp_flows_detailed(self, udp_packets: List[Packet]) -> Dict[str, Any]:
        """
        详细分析UDP流
        
        对UDP流进行深度分析，包括流模式识别、方向性分析、
        应用协议识别和流质量评估。
        
        Args:
            udp_packets: UDP数据包列表
            
        Returns:
            Dict[str, Any]: UDP流分析结果
        """
        if not udp_packets:
            return {
                'flow_count': 0,
                'flow_durations': [],
                'flow_patterns': {},
                'application_protocols': {},
                'flow_quality': {}
            }
        
        # 基于五元组识别流
        flows = {}
        
        for packet in udp_packets:
            if packet.src_ip and packet.dst_ip and packet.src_port and packet.dst_port:
                # 标准化流键
                flow_key = self._normalize_flow_key(
                    packet.src_ip, packet.src_port,
                    packet.dst_ip, packet.dst_port
                )
                
                if flow_key not in flows:
                    flows[flow_key] = {
                        'first_seen': packet.timestamp,
                        'last_seen': packet.timestamp,
                        'packet_count': 0,
                        'total_bytes': 0,
                        'src_to_dst_packets': 0,
                        'dst_to_src_packets': 0,
                        'src_to_dst_bytes': 0,
                        'dst_to_src_bytes': 0,
                        'application_protocol': 'unknown',
                        'packet_sizes': []
                    }
                
                # 更新流统计
                flow_stats = flows[flow_key]
                flow_stats['last_seen'] = packet.timestamp
                flow_stats['packet_count'] += 1
                flow_stats['total_bytes'] += packet.size
                flow_stats['packet_sizes'].append(packet.size)
                
                # 分析流方向
                original_src = flow_key[0]
                if packet.src_ip == original_src:
                    flow_stats['src_to_dst_packets'] += 1
                    flow_stats['src_to_dst_bytes'] += packet.size
                else:
                    flow_stats['dst_to_src_packets'] += 1
                    flow_stats['dst_to_src_bytes'] += packet.size
                
                # 识别应用协议
                app_protocol = self._identify_udp_application_protocol(packet)
                if app_protocol != 'unknown':
                    flow_stats['application_protocol'] = app_protocol
        
        # 分析流模式
        flow_patterns = self._analyze_udp_flow_patterns(flows)
        
        # 统计应用协议分布
        app_protocols = Counter()
        for flow_stats in flows.values():
            app_protocols[flow_stats['application_protocol']] += 1
        
        # 计算流持续时间
        flow_durations = []
        for flow_stats in flows.values():
            if flow_stats['first_seen'] and flow_stats['last_seen']:
                duration = (flow_stats['last_seen'] - flow_stats['first_seen']).total_seconds()
                flow_durations.append(duration)
        
        # 计算流质量指标
        flow_quality = self._calculate_udp_flow_quality(flows)
        
        return {
            'flow_count': len(flows),
            'flow_durations': flow_durations,
            'flow_patterns': flow_patterns,
            'application_protocols': dict(app_protocols),
            'flow_quality': flow_quality
        }

    def _normalize_flow_key(self, src_ip: str, src_port: int,
                           dst_ip: str, dst_port: int) -> tuple:
        """
        标准化流键值
        
        Args:
            src_ip: 源IP地址
            src_port: 源端口
            dst_ip: 目标IP地址
            dst_port: 目标端口
            
        Returns:
            tuple: 标准化的流键值
        """
        endpoint1 = (src_ip, src_port)
        endpoint2 = (dst_ip, dst_port)
        
        if endpoint1 < endpoint2:
            return (src_ip, src_port, dst_ip, dst_port)
        else:
            return (dst_ip, dst_port, src_ip, src_port)

    def _identify_udp_application_protocol(self, packet: Packet) -> str:
        """
        识别UDP应用层协议
        
        Args:
            packet: UDP数据包
            
        Returns:
            str: 应用协议名称
        """
        # 基于端口号识别常见的UDP协议
        well_known_ports = {
            53: 'dns',
            67: 'dhcp_server',
            68: 'dhcp_client',
            69: 'tftp',
            123: 'ntp',
            161: 'snmp',
            162: 'snmp_trap',
            514: 'syslog',
            520: 'rip',
            1900: 'upnp',
            5353: 'mdns',
            1194: 'openvpn',
            500: 'ipsec',
            4500: 'ipsec_nat',
            1701: 'l2tp'
        }
        
        # 检查目标端口
        if packet.dst_port in well_known_ports:
            return well_known_ports[packet.dst_port]
        
        # 检查源端口
        if packet.src_port in well_known_ports:
            return well_known_ports[packet.src_port]
        
        # 检查端口范围
        if packet.dst_port and packet.dst_port >= 32768:
            return 'ephemeral'
        elif packet.src_port and packet.src_port >= 32768:
            return 'ephemeral'
        
        return 'unknown'

    def _analyze_udp_flow_patterns(self, flows: Dict) -> Dict[str, int]:
        """
        分析UDP流模式
        
        Args:
            flows: UDP流统计字典
            
        Returns:
            Dict[str, int]: 流模式分布统计
        """
        pattern_counts = {
            'unidirectional': 0,    # 单向流
            'bidirectional': 0,     # 双向流
            'dns_query': 0,         # DNS查询
            'dhcp_transaction': 0,  # DHCP事务
            'streaming': 0,         # 流媒体
            'bulk_transfer': 0,     # 批量传输
            'interactive': 0,       # 交互式
            'broadcast': 0,         # 广播
            'multicast': 0          # 组播
        }
        
        for flow_key, stats in flows.items():
            # 判断流方向性
            if stats['dst_to_src_packets'] == 0:
                pattern_counts['unidirectional'] += 1
            else:
                pattern_counts['bidirectional'] += 1
            
            # 基于应用协议分类
            app_protocol = stats['application_protocol']
            if app_protocol == 'dns':
                pattern_counts['dns_query'] += 1
            elif app_protocol in ['dhcp_server', 'dhcp_client']:
                pattern_counts['dhcp_transaction'] += 1
            
            # 基于流量特征分类
            duration = (stats['last_seen'] - stats['first_seen']).total_seconds()
            avg_packet_size = stats['total_bytes'] / stats['packet_count'] if stats['packet_count'] > 0 else 0
            
            if stats['total_bytes'] > 1000000:  # 超过1MB
                pattern_counts['bulk_transfer'] += 1
            elif duration > 60 and stats['packet_count'] > 100:  # 长时间多包
                pattern_counts['streaming'] += 1
            elif avg_packet_size < 100:  # 小包
                pattern_counts['interactive'] += 1
            
            # 检查广播和组播（基于IP地址）
            dst_ip = flow_key[2]
            try:
                ip_addr = ipaddress.ip_address(dst_ip)
                if ip_addr.is_multicast:
                    pattern_counts['multicast'] += 1
                elif str(ip_addr).endswith('.255'):  # 简单的广播检测
                    pattern_counts['broadcast'] += 1
            except ValueError:
                pass  # 无效IP地址
        
        return pattern_counts

    def _calculate_udp_flow_quality(self, flows: Dict) -> Dict[str, float]:
        """
        计算UDP流质量指标
        
        Args:
            flows: UDP流统计字典
            
        Returns:
            Dict[str, float]: 流质量指标
        """
        if not flows:
            return {}
        
        total_flows = len(flows)
        total_packets = sum(stats['packet_count'] for stats in flows.values())
        
        # 双向流比例
        bidirectional_flows = sum(1 for stats in flows.values() 
                                if stats['dst_to_src_packets'] > 0)
        bidirectional_ratio = bidirectional_flows / total_flows if total_flows > 0 else 0
        
        # 平均包数每流
        avg_packets_per_flow = total_packets / total_flows if total_flows > 0 else 0
        
        # 流持续时间统计
        durations = []
        for stats in flows.values():
            if stats['first_seen'] and stats['last_seen']:
                duration = (stats['last_seen'] - stats['first_seen']).total_seconds()
                durations.append(duration)
        
        avg_duration = statistics.mean(durations) if durations else 0
        
        # 包大小一致性（标准差）
        all_packet_sizes = []
        for stats in flows.values():
            all_packet_sizes.extend(stats['packet_sizes'])
        
        packet_size_consistency = 1.0 / (1.0 + statistics.stdev(all_packet_sizes)) if len(all_packet_sizes) > 1 else 1.0
        
        return {
            'bidirectional_ratio': bidirectional_ratio,
            'average_packets_per_flow': avg_packets_per_flow,
            'average_flow_duration': avg_duration,
            'packet_size_consistency': packet_size_consistency,
            'flow_efficiency': avg_packets_per_flow / avg_duration if avg_duration > 0 else 0
        }
