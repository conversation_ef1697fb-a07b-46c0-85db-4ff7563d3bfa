#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强流量统计分析模块
Enhanced Traffic Statistics Analysis Module

提供完整的网络流量统计分析功能，包括：
- 基础流量指标计算（带宽、包速率、连接数等）
- 时间序列数据生成和分析
- TCP连接状态深度分析
- UDP流模式识别
- 地理位置分析
- Top N统计排序
- 流量模式识别和质量评估

实现了高性能的流量分析算法，支持大规模数据包处理。
"""

import logging
import statistics
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import ipaddress
from dataclasses import asdict

from ..core.models import (
    Packet, TrafficStats, ProtocolType, PacketDirection
)
from ..core.exceptions import AnalysisError
from .traffic_analyzer_extended import TrafficAnalyzerExtensions

logger = logging.getLogger(__name__)


class EnhancedTrafficAnalyzer:
    """
    增强流量统计分析器
    
    提供完整的网络流量分析功能，包括基础统计、时间序列分析、
    连接分析、地理分布分析和流量模式识别。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化增强流量分析器
        
        Args:
            config: 分析器配置参数，包括：
                - top_n_limit: Top N统计的数量限制（默认10）
                - time_window_seconds: 时间窗口大小（默认60秒）
                - enable_geo_analysis: 是否启用地理位置分析（默认False）
                - enable_quality_metrics: 是否启用质量指标计算（默认True）
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.EnhancedTrafficAnalyzer")
        
        # 配置参数
        self.top_n_limit = self.config.get('top_n_limit', 10)
        self.time_window_seconds = self.config.get('time_window_seconds', 60)
        self.enable_geo_analysis = self.config.get('enable_geo_analysis', False)
        self.enable_quality_metrics = self.config.get('enable_quality_metrics', True)

        # 初始化扩展方法
        self.extensions = TrafficAnalyzerExtensions(self.top_n_limit)

        self.logger.info(f"增强流量分析器初始化完成，配置: {self.config}")

    def analyze_traffic_comprehensive(self, packets: List[Packet]) -> TrafficStats:
        """
        执行全面的流量统计分析
        
        对数据包进行完整的流量分析，包括所有统计指标、
        时间序列分析、连接分析和模式识别。
        
        Args:
            packets: 数据包列表
            
        Returns:
            TrafficStats: 完整的流量统计结果
            
        Raises:
            AnalysisError: 分析过程中出现错误
        """
        if not packets:
            self.logger.warning("数据包列表为空，返回空的流量统计结果")
            return TrafficStats()
            
        try:
            self.logger.info(f"开始全面分析 {len(packets)} 个数据包的流量统计信息")
            
            # 1. 基础流量统计计算
            basic_stats = self._calculate_basic_statistics(packets)
            
            # 2. 时间序列数据生成
            time_series_data = self._generate_time_series_data(packets)
            
            # 3. 连接状态分析
            connection_stats = self._analyze_connection_states(packets)
            
            # 4. 地理位置分析（如果启用）
            geo_distribution = {}
            if self.enable_geo_analysis:
                geo_distribution = self._analyze_geographic_distribution(packets)
            
            # 5. Top N统计生成
            top_n_stats = self._generate_top_n_statistics(packets)
            
            # 6. 流量模式识别
            traffic_patterns = self._identify_traffic_patterns(packets, time_series_data)
            
            # 7. 质量指标计算（如果启用）
            quality_metrics = {}
            if self.enable_quality_metrics:
                quality_metrics = self._calculate_quality_metrics(packets, basic_stats)
            
            # 构建完整的流量统计结果
            traffic_stats = TrafficStats(
                # 基础统计
                **basic_stats,
                
                # 连接统计
                **connection_stats,
                
                # 时间序列数据
                time_series_data=time_series_data,
                
                # 地理分布
                geo_distribution=geo_distribution,
                
                # Top N统计
                top_n_statistics=top_n_stats,
                
                # 流量模式
                traffic_patterns=traffic_patterns,
                
                # 质量指标
                quality_metrics=quality_metrics
            )
            
            self.logger.info(f"全面流量分析完成: {basic_stats['total_packets']}包, "
                           f"{basic_stats['total_bytes']}字节, "
                           f"{basic_stats['packets_per_second']:.2f}包/秒")
            
            return traffic_stats
            
        except Exception as e:
            error_msg = f"全面流量统计分析失败: {str(e)}"
            self.logger.error(error_msg)
            raise AnalysisError(error_msg) from e

    def _calculate_basic_statistics(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        计算基础流量统计指标
        
        包括包数量、字节数、速率、包大小分布等基础指标。
        
        Args:
            packets: 数据包列表
            
        Returns:
            Dict[str, Any]: 基础统计指标字典
        """
        self.logger.debug("开始计算基础流量统计指标")
        
        # 基础计数
        total_packets = len(packets)
        total_bytes = sum(p.size for p in packets)
        
        # 包大小统计
        packet_sizes = [p.size for p in packets]
        min_packet_size = min(packet_sizes) if packet_sizes else 0
        max_packet_size = max(packet_sizes) if packet_sizes else 0
        average_packet_size = total_bytes / total_packets if total_packets > 0 else 0
        packet_size_variance = statistics.variance(packet_sizes) if len(packet_sizes) > 1 else 0
        
        # 时间范围分析
        timestamps = [p.timestamp for p in packets if p.timestamp]
        start_time = min(timestamps) if timestamps else None
        end_time = max(timestamps) if timestamps else None
        duration = (end_time - start_time).total_seconds() if start_time and end_time else 0.0
        
        # 速率计算
        packets_per_second = total_packets / duration if duration > 0 else 0
        bytes_per_second = total_bytes / duration if duration > 0 else 0
        bits_per_second = bytes_per_second * 8
        
        # 方向统计
        direction_stats = self._calculate_direction_statistics(packets)
        
        # 协议分布
        protocol_distribution = Counter()
        for packet in packets:
            protocol = packet.protocol.value if packet.protocol else 'unknown'
            protocol_distribution[protocol] += 1
        
        # 端口分布
        port_distribution = Counter()
        for packet in packets:
            if packet.src_port:
                port_distribution[f"src_{packet.src_port}"] += 1
            if packet.dst_port:
                port_distribution[f"dst_{packet.dst_port}"] += 1
        
        return {
            'total_packets': total_packets,
            'total_bytes': total_bytes,
            'average_packet_size': average_packet_size,
            'min_packet_size': min_packet_size,
            'max_packet_size': max_packet_size,
            'packet_size_variance': packet_size_variance,
            'packets_per_second': packets_per_second,
            'bytes_per_second': bytes_per_second,
            'bits_per_second': bits_per_second,
            'start_time': start_time,
            'end_time': end_time,
            'duration_seconds': duration,
            'protocol_distribution': dict(protocol_distribution),
            'port_distribution': dict(port_distribution),
            **direction_stats
        }

    def _calculate_direction_statistics(self, packets: List[Packet]) -> Dict[str, int]:
        """
        计算流量方向统计
        
        Args:
            packets: 数据包列表
            
        Returns:
            Dict[str, int]: 方向统计字典
        """
        inbound_packets = sum(1 for p in packets if p.direction == PacketDirection.INBOUND)
        outbound_packets = sum(1 for p in packets if p.direction == PacketDirection.OUTBOUND)
        internal_packets = sum(1 for p in packets if p.direction == PacketDirection.INTERNAL)
        
        inbound_bytes = sum(p.size for p in packets if p.direction == PacketDirection.INBOUND)
        outbound_bytes = sum(p.size for p in packets if p.direction == PacketDirection.OUTBOUND)
        internal_bytes = sum(p.size for p in packets if p.direction == PacketDirection.INTERNAL)
        
        return {
            'inbound_packets': inbound_packets,
            'outbound_packets': outbound_packets,
            'internal_packets': internal_packets,
            'inbound_bytes': inbound_bytes,
            'outbound_bytes': outbound_bytes,
            'internal_bytes': internal_bytes
        }

    def _generate_time_series_data(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        生成时间序列数据和分析

        按时间窗口统计流量数据，生成时间序列分析结果，
        包括趋势分析、峰值检测和周期性分析。

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: 时间序列数据和分析结果
        """
        self.logger.debug("开始生成时间序列数据")

        if not packets:
            return {}

        # 获取时间范围
        timestamps = [p.timestamp for p in packets if p.timestamp]
        if not timestamps:
            return {}

        start_time = min(timestamps)
        end_time = max(timestamps)
        duration = (end_time - start_time).total_seconds()

        # 生成时间窗口
        time_windows = []
        current_time = start_time
        window_size = timedelta(seconds=self.time_window_seconds)

        while current_time <= end_time:
            window_end = min(current_time + window_size, end_time)
            time_windows.append((current_time, window_end))
            current_time = window_end

        # 按时间窗口统计数据
        packet_timeline = []
        byte_timeline = []
        connection_timeline = []

        for window_start, window_end in time_windows:
            # 筛选时间窗口内的数据包
            window_packets = [
                p for p in packets
                if p.timestamp and window_start <= p.timestamp < window_end
            ]

            packet_count = len(window_packets)
            byte_count = sum(p.size for p in window_packets)

            # 统计连接数（基于唯一的源-目标IP对）
            connections = set()
            for p in window_packets:
                if p.src_ip and p.dst_ip:
                    connections.add((p.src_ip, p.dst_ip))
            connection_count = len(connections)

            # 添加到时间线
            timestamp_str = window_start.strftime('%Y-%m-%d %H:%M:%S')
            packet_timeline.append((timestamp_str, packet_count))
            byte_timeline.append((timestamp_str, byte_count))
            connection_timeline.append((timestamp_str, connection_count))

        # 时间序列统计分析
        packet_counts = [count for _, count in packet_timeline]
        byte_counts = [count for _, count in byte_timeline]

        statistics_data = {
            'packet_statistics': self._calculate_time_series_statistics(packet_counts),
            'byte_statistics': self._calculate_time_series_statistics(byte_counts),
            'peak_detection': self._detect_traffic_peaks(packet_timeline, byte_timeline),
            'trend_analysis': self._analyze_traffic_trends(packet_counts, byte_counts)
        }

        return {
            'packet_timeline': packet_timeline,
            'byte_timeline': byte_timeline,
            'connection_timeline': connection_timeline,
            'window_size_seconds': self.time_window_seconds,
            'total_windows': len(time_windows),
            'statistics': statistics_data
        }

    def _calculate_time_series_statistics(self, values: List[int]) -> Dict[str, float]:
        """
        计算时间序列统计指标

        Args:
            values: 时间序列数值列表

        Returns:
            Dict[str, float]: 统计指标字典
        """
        if not values:
            return {}

        return {
            'mean': statistics.mean(values),
            'median': statistics.median(values),
            'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
            'min': min(values),
            'max': max(values),
            'variance': statistics.variance(values) if len(values) > 1 else 0,
            'coefficient_of_variation': statistics.stdev(values) / statistics.mean(values) if len(values) > 1 and statistics.mean(values) > 0 else 0
        }

    def _detect_traffic_peaks(self, packet_timeline: List[Tuple],
                            byte_timeline: List[Tuple]) -> Dict[str, Any]:
        """
        检测流量峰值

        Args:
            packet_timeline: 包数量时间线
            byte_timeline: 字节数时间线

        Returns:
            Dict[str, Any]: 峰值检测结果
        """
        packet_counts = [count for _, count in packet_timeline]
        byte_counts = [count for _, count in byte_timeline]

        if not packet_counts or not byte_counts:
            return {}

        # 计算阈值（平均值 + 2倍标准差）
        packet_mean = statistics.mean(packet_counts)
        packet_std = statistics.stdev(packet_counts) if len(packet_counts) > 1 else 0
        packet_threshold = packet_mean + 2 * packet_std

        byte_mean = statistics.mean(byte_counts)
        byte_std = statistics.stdev(byte_counts) if len(byte_counts) > 1 else 0
        byte_threshold = byte_mean + 2 * byte_std

        # 检测峰值
        packet_peaks = []
        byte_peaks = []

        for i, (timestamp, count) in enumerate(packet_timeline):
            if count > packet_threshold:
                packet_peaks.append({
                    'timestamp': timestamp,
                    'value': count,
                    'threshold': packet_threshold,
                    'index': i
                })

        for i, (timestamp, count) in enumerate(byte_timeline):
            if count > byte_threshold:
                byte_peaks.append({
                    'timestamp': timestamp,
                    'value': count,
                    'threshold': byte_threshold,
                    'index': i
                })

        return {
            'packet_peaks': packet_peaks,
            'byte_peaks': byte_peaks,
            'packet_peak_count': len(packet_peaks),
            'byte_peak_count': len(byte_peaks),
            'packet_threshold': packet_threshold,
            'byte_threshold': byte_threshold
        }

    def _analyze_traffic_trends(self, packet_counts: List[int],
                              byte_counts: List[int]) -> Dict[str, str]:
        """
        分析流量趋势

        Args:
            packet_counts: 包数量序列
            byte_counts: 字节数序列

        Returns:
            Dict[str, str]: 趋势分析结果
        """
        if len(packet_counts) < 3 or len(byte_counts) < 3:
            return {'trend': 'insufficient_data'}

        # 简单的趋势分析：比较前1/3和后1/3的平均值
        third = len(packet_counts) // 3

        packet_early_avg = statistics.mean(packet_counts[:third])
        packet_late_avg = statistics.mean(packet_counts[-third:])

        byte_early_avg = statistics.mean(byte_counts[:third])
        byte_late_avg = statistics.mean(byte_counts[-third:])

        # 判断趋势
        packet_trend = 'stable'
        if packet_late_avg > packet_early_avg * 1.2:
            packet_trend = 'increasing'
        elif packet_late_avg < packet_early_avg * 0.8:
            packet_trend = 'decreasing'

        byte_trend = 'stable'
        if byte_late_avg > byte_early_avg * 1.2:
            byte_trend = 'increasing'
        elif byte_late_avg < byte_early_avg * 0.8:
            byte_trend = 'decreasing'

        # 综合趋势
        if packet_trend == byte_trend:
            overall_trend = packet_trend
        else:
            overall_trend = 'mixed'

        return {
            'packet_trend': packet_trend,
            'byte_trend': byte_trend,
            'overall_trend': overall_trend,
            'packet_change_ratio': packet_late_avg / packet_early_avg if packet_early_avg > 0 else 0,
            'byte_change_ratio': byte_late_avg / byte_early_avg if byte_early_avg > 0 else 0
        }

    def _analyze_connection_states(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        分析TCP连接状态和UDP流

        对TCP连接进行生命周期分析，对UDP流进行模式识别，
        计算连接质量指标和成功率。

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: 连接状态分析结果
        """
        self.logger.debug("开始分析连接状态")

        # 分离TCP和UDP数据包
        tcp_packets = [p for p in packets if p.protocol == ProtocolType.TCP]
        udp_packets = [p for p in packets if p.protocol == ProtocolType.UDP]

        # TCP连接分析
        tcp_analysis = self._analyze_tcp_connections_detailed(tcp_packets)

        # UDP流分析
        udp_analysis = self.extensions.analyze_udp_flows_detailed(udp_packets)

        # 总连接统计
        total_connections = tcp_analysis['connection_count'] + udp_analysis['flow_count']
        active_connections = tcp_analysis['active_connections']
        failed_connections = tcp_analysis['failed_connections']

        # 连接成功率
        connection_success_rate = 0.0
        if total_connections > 0:
            connection_success_rate = (total_connections - failed_connections) / total_connections

        # 平均连接持续时间
        all_durations = tcp_analysis['connection_durations'] + udp_analysis['flow_durations']
        average_connection_duration = statistics.mean(all_durations) if all_durations else 0.0

        return {
            'total_connections': total_connections,
            'active_connections': active_connections,
            'failed_connections': failed_connections,
            'tcp_connections': tcp_analysis['connection_count'],
            'udp_flows': udp_analysis['flow_count'],
            'connection_success_rate': connection_success_rate,
            'average_connection_duration': average_connection_duration,
            'tcp_analysis': tcp_analysis,
            'udp_analysis': udp_analysis
        }

    def _analyze_tcp_connections_detailed(self, tcp_packets: List[Packet]) -> Dict[str, Any]:
        """
        详细分析TCP连接

        Args:
            tcp_packets: TCP数据包列表

        Returns:
            Dict[str, Any]: TCP连接分析结果
        """
        if not tcp_packets:
            return {
                'connection_count': 0,
                'active_connections': 0,
                'failed_connections': 0,
                'connection_durations': [],
                'connection_states': {},
                'connection_quality': {}
            }

        # 基于五元组识别连接
        connections = {}

        for packet in tcp_packets:
            if packet.src_ip and packet.dst_ip and packet.src_port and packet.dst_port:
                # 标准化连接键
                conn_key = self._normalize_connection_key(
                    packet.src_ip, packet.src_port,
                    packet.dst_ip, packet.dst_port
                )

                if conn_key not in connections:
                    connections[conn_key] = {
                        'first_seen': packet.timestamp,
                        'last_seen': packet.timestamp,
                        'packet_count': 0,
                        'total_bytes': 0,
                        'syn_count': 0,
                        'fin_count': 0,
                        'rst_count': 0,
                        'ack_count': 0,
                        'retransmissions': 0,
                        'out_of_order': 0
                    }

                # 更新连接统计
                conn_stats = connections[conn_key]
                conn_stats['last_seen'] = packet.timestamp
                conn_stats['packet_count'] += 1
                conn_stats['total_bytes'] += packet.size

                # 分析TCP标志位（如果可用）
                if hasattr(packet, 'tcp_flags'):
                    flags = packet.tcp_flags
                    if flags & 0x02:  # SYN
                        conn_stats['syn_count'] += 1
                    if flags & 0x01:  # FIN
                        conn_stats['fin_count'] += 1
                    if flags & 0x04:  # RST
                        conn_stats['rst_count'] += 1
                    if flags & 0x10:  # ACK
                        conn_stats['ack_count'] += 1

        # 分析连接状态和质量
        connection_states = self._classify_tcp_connection_states(connections)
        connection_durations = []

        for conn_key, stats in connections.items():
            if stats['first_seen'] and stats['last_seen']:
                duration = (stats['last_seen'] - stats['first_seen']).total_seconds()
                connection_durations.append(duration)

        # 计算连接质量指标
        quality_metrics = self._calculate_tcp_connection_quality(connections)

        return {
            'connection_count': len(connections),
            'active_connections': connection_states.get('established', 0),
            'failed_connections': connection_states.get('reset', 0) + connection_states.get('failed', 0),
            'connection_durations': connection_durations,
            'connection_states': connection_states,
            'connection_quality': quality_metrics
        }

    def _normalize_connection_key(self, src_ip: str, src_port: int,
                                dst_ip: str, dst_port: int) -> tuple:
        """
        标准化连接键值，确保同一连接的双向流量使用相同的键

        Args:
            src_ip: 源IP地址
            src_port: 源端口
            dst_ip: 目标IP地址
            dst_port: 目标端口

        Returns:
            tuple: 标准化的连接键值
        """
        endpoint1 = (src_ip, src_port)
        endpoint2 = (dst_ip, dst_port)

        if endpoint1 < endpoint2:
            return (src_ip, src_port, dst_ip, dst_port)
        else:
            return (dst_ip, dst_port, src_ip, src_port)

    def _classify_tcp_connection_states(self, connections: Dict) -> Dict[str, int]:
        """
        分类TCP连接状态

        Args:
            connections: 连接统计字典

        Returns:
            Dict[str, int]: 连接状态分布
        """
        state_counts = {
            'established': 0,    # 已建立连接
            'syn_sent': 0,       # SYN已发送
            'syn_received': 0,   # SYN已接收
            'fin_wait': 0,       # FIN等待
            'closed': 0,         # 已关闭
            'reset': 0,          # 重置连接
            'failed': 0,         # 连接失败
            'unknown': 0         # 未知状态
        }

        for conn_key, stats in connections.items():
            if stats['rst_count'] > 0:
                state_counts['reset'] += 1
            elif stats['fin_count'] >= 2:  # 双向FIN
                state_counts['closed'] += 1
            elif stats['fin_count'] > 0:
                state_counts['fin_wait'] += 1
            elif stats['syn_count'] > 0 and stats['ack_count'] > 0:
                state_counts['established'] += 1
            elif stats['syn_count'] > 0:
                state_counts['syn_sent'] += 1
            elif stats['packet_count'] == 1 and stats['syn_count'] == 0:
                state_counts['failed'] += 1
            else:
                state_counts['unknown'] += 1

        return state_counts

    def _calculate_tcp_connection_quality(self, connections: Dict) -> Dict[str, float]:
        """
        计算TCP连接质量指标

        Args:
            connections: 连接统计字典

        Returns:
            Dict[str, float]: 连接质量指标
        """
        if not connections:
            return {}

        total_connections = len(connections)
        total_packets = sum(stats['packet_count'] for stats in connections.values())
        total_retransmissions = sum(stats.get('retransmissions', 0) for stats in connections.values())

        # 重传率
        retransmission_rate = total_retransmissions / total_packets if total_packets > 0 else 0

        # 平均包数每连接
        avg_packets_per_connection = total_packets / total_connections if total_connections > 0 else 0

        # 连接效率（基于包数量和持续时间）
        durations = []
        for stats in connections.values():
            if stats['first_seen'] and stats['last_seen']:
                duration = (stats['last_seen'] - stats['first_seen']).total_seconds()
                if duration > 0:
                    durations.append(duration)

        avg_duration = statistics.mean(durations) if durations else 0

        return {
            'retransmission_rate': retransmission_rate,
            'average_packets_per_connection': avg_packets_per_connection,
            'average_connection_duration': avg_duration,
            'connection_efficiency': avg_packets_per_connection / avg_duration if avg_duration > 0 else 0
        }

    def _analyze_geographic_distribution(self, packets: List[Packet]) -> Dict[str, int]:
        """
        分析地理位置分布

        基于IP地址分析流量的地理分布，包括私有网络、
        公网地址、环回地址等分类。

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, int]: 地理位置分布统计
        """
        self.logger.debug("开始分析地理位置分布")

        geo_dist = defaultdict(int)
        ip_addresses = set()

        # 收集所有IP地址
        for packet in packets:
            if packet.src_ip:
                ip_addresses.add(packet.src_ip)
            if packet.dst_ip:
                ip_addresses.add(packet.dst_ip)

        # 分类IP地址
        for ip_str in ip_addresses:
            try:
                ip = ipaddress.ip_address(ip_str)
                if ip.is_private:
                    geo_dist['Private'] += 1
                elif ip.is_loopback:
                    geo_dist['Loopback'] += 1
                elif ip.is_multicast:
                    geo_dist['Multicast'] += 1
                elif ip.is_link_local:
                    geo_dist['Link_Local'] += 1
                else:
                    geo_dist['Public'] += 1
            except ValueError:
                geo_dist['Invalid'] += 1

        # 统计每类IP的流量
        traffic_dist = defaultdict(int)
        for packet in packets:
            for ip_field in [packet.src_ip, packet.dst_ip]:
                if ip_field:
                    try:
                        ip = ipaddress.ip_address(ip_field)
                        if ip.is_private:
                            traffic_dist['Private_Traffic'] += packet.size
                        elif ip.is_loopback:
                            traffic_dist['Loopback_Traffic'] += packet.size
                        elif ip.is_multicast:
                            traffic_dist['Multicast_Traffic'] += packet.size
                        elif ip.is_link_local:
                            traffic_dist['Link_Local_Traffic'] += packet.size
                        else:
                            traffic_dist['Public_Traffic'] += packet.size
                    except ValueError:
                        traffic_dist['Invalid_Traffic'] += packet.size

        # 合并结果
        result = dict(geo_dist)
        result.update(traffic_dist)

        return result

    def _generate_top_n_statistics(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        生成Top N统计信息

        统计最活跃的IP地址、端口、协议等，提供排序后的统计结果。

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: Top N统计结果
        """
        self.logger.debug("开始生成Top N统计信息")

        # 初始化计数器
        src_ip_counter = Counter()
        dst_ip_counter = Counter()
        src_port_counter = Counter()
        dst_port_counter = Counter()
        protocol_counter = Counter()

        # 字节统计
        src_ip_bytes = defaultdict(int)
        dst_ip_bytes = defaultdict(int)
        protocol_bytes = defaultdict(int)

        # 统计数据
        for packet in packets:
            # IP统计
            if packet.src_ip:
                src_ip_counter[packet.src_ip] += 1
                src_ip_bytes[packet.src_ip] += packet.size
            if packet.dst_ip:
                dst_ip_counter[packet.dst_ip] += 1
                dst_ip_bytes[packet.dst_ip] += packet.size

            # 端口统计
            if packet.src_port:
                src_port_counter[packet.src_port] += 1
            if packet.dst_port:
                dst_port_counter[packet.dst_port] += 1

            # 协议统计
            protocol = packet.protocol.value if packet.protocol else 'unknown'
            protocol_counter[protocol] += 1
            protocol_bytes[protocol] += packet.size

        # 生成Top N结果
        return {
            'top_source_ips': [
                {
                    'ip': ip,
                    'packets': count,
                    'bytes': src_ip_bytes[ip],
                    'percentage': count / len(packets) * 100
                }
                for ip, count in src_ip_counter.most_common(self.top_n_limit)
            ],
            'top_destination_ips': [
                {
                    'ip': ip,
                    'packets': count,
                    'bytes': dst_ip_bytes[ip],
                    'percentage': count / len(packets) * 100
                }
                for ip, count in dst_ip_counter.most_common(self.top_n_limit)
            ],
            'top_source_ports': [
                {
                    'port': port,
                    'packets': count,
                    'percentage': count / len(packets) * 100
                }
                for port, count in src_port_counter.most_common(self.top_n_limit)
            ],
            'top_destination_ports': [
                {
                    'port': port,
                    'packets': count,
                    'percentage': count / len(packets) * 100
                }
                for port, count in dst_port_counter.most_common(self.top_n_limit)
            ],
            'top_protocols': [
                {
                    'protocol': protocol,
                    'packets': count,
                    'bytes': protocol_bytes[protocol],
                    'percentage': count / len(packets) * 100
                }
                for protocol, count in protocol_counter.most_common(self.top_n_limit)
            ]
        }

    def _identify_traffic_patterns(self, packets: List[Packet],
                                 time_series_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        识别流量模式和特征

        分析流量类型、应用模式、时间模式等，提供流量特征识别。

        Args:
            packets: 数据包列表
            time_series_data: 时间序列数据

        Returns:
            Dict[str, Any]: 流量模式识别结果
        """
        self.logger.debug("开始识别流量模式")

        if not packets or not time_series_data:
            return {}

        # 流量类型分析
        traffic_type = self._classify_traffic_type(packets, time_series_data)

        # 应用模式分析
        application_patterns = self._analyze_application_patterns(packets)

        # 时间模式分析
        temporal_patterns = self._analyze_temporal_patterns(time_series_data)

        # 网络行为分析
        network_behavior = self._analyze_network_behavior(packets)

        return {
            'traffic_type': traffic_type,
            'application_patterns': application_patterns,
            'temporal_patterns': temporal_patterns,
            'network_behavior': network_behavior
        }

    def _classify_traffic_type(self, packets: List[Packet],
                             time_series_data: Dict[str, Any]) -> Dict[str, str]:
        """
        分类流量类型

        Args:
            packets: 数据包列表
            time_series_data: 时间序列数据

        Returns:
            Dict[str, str]: 流量类型分类结果
        """
        # 获取时间序列统计
        packet_stats = time_series_data.get('statistics', {}).get('packet_statistics', {})

        # 流量突发性分析
        cv = packet_stats.get('coefficient_of_variation', 0)
        if cv > 1.0:
            burstiness = 'high'
        elif cv > 0.5:
            burstiness = 'medium'
        else:
            burstiness = 'low'

        # 流量规模分析
        total_packets = len(packets)
        if total_packets > 100000:
            volume = 'high'
        elif total_packets > 10000:
            volume = 'medium'
        else:
            volume = 'low'

        # 协议分布分析
        tcp_count = sum(1 for p in packets if p.protocol == ProtocolType.TCP)
        udp_count = sum(1 for p in packets if p.protocol == ProtocolType.UDP)

        if tcp_count > udp_count * 3:
            protocol_dominance = 'tcp_dominant'
        elif udp_count > tcp_count * 3:
            protocol_dominance = 'udp_dominant'
        else:
            protocol_dominance = 'mixed'

        return {
            'burstiness': burstiness,
            'volume': volume,
            'protocol_dominance': protocol_dominance
        }

    def _analyze_application_patterns(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        分析应用模式

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: 应用模式分析结果
        """
        # 端口分析
        well_known_ports = {80, 443, 53, 25, 110, 143, 21, 22, 23}
        well_known_count = sum(1 for p in packets
                             if p.dst_port in well_known_ports or p.src_port in well_known_ports)

        well_known_ratio = well_known_count / len(packets) if packets else 0

        # 包大小分析
        packet_sizes = [p.size for p in packets]
        avg_size = statistics.mean(packet_sizes) if packet_sizes else 0

        if avg_size < 100:
            size_pattern = 'small_packets'
        elif avg_size > 1000:
            size_pattern = 'large_packets'
        else:
            size_pattern = 'mixed_sizes'

        return {
            'well_known_port_ratio': well_known_ratio,
            'size_pattern': size_pattern,
            'average_packet_size': avg_size
        }

    def _analyze_temporal_patterns(self, time_series_data: Dict[str, Any]) -> Dict[str, str]:
        """
        分析时间模式

        Args:
            time_series_data: 时间序列数据

        Returns:
            Dict[str, str]: 时间模式分析结果
        """
        trend_analysis = time_series_data.get('statistics', {}).get('trend_analysis', {})
        peak_detection = time_series_data.get('statistics', {}).get('peak_detection', {})

        overall_trend = trend_analysis.get('overall_trend', 'unknown')
        peak_count = peak_detection.get('packet_peak_count', 0)

        if peak_count > 5:
            peak_pattern = 'highly_variable'
        elif peak_count > 2:
            peak_pattern = 'moderately_variable'
        else:
            peak_pattern = 'stable'

        return {
            'trend': overall_trend,
            'peak_pattern': peak_pattern
        }

    def _analyze_network_behavior(self, packets: List[Packet]) -> Dict[str, Any]:
        """
        分析网络行为

        Args:
            packets: 数据包列表

        Returns:
            Dict[str, Any]: 网络行为分析结果
        """
        # 连接多样性
        unique_connections = set()
        for p in packets:
            if p.src_ip and p.dst_ip:
                unique_connections.add((p.src_ip, p.dst_ip))

        connection_diversity = len(unique_connections) / len(packets) if packets else 0

        # 方向性分析
        inbound = sum(1 for p in packets if p.direction == PacketDirection.INBOUND)
        outbound = sum(1 for p in packets if p.direction == PacketDirection.OUTBOUND)

        if inbound > outbound * 2:
            direction_bias = 'inbound_heavy'
        elif outbound > inbound * 2:
            direction_bias = 'outbound_heavy'
        else:
            direction_bias = 'balanced'

        return {
            'connection_diversity': connection_diversity,
            'direction_bias': direction_bias
        }

    def _calculate_quality_metrics(self, packets: List[Packet],
                                 basic_stats: Dict[str, Any]) -> Dict[str, float]:
        """
        计算质量指标

        Args:
            packets: 数据包列表
            basic_stats: 基础统计信息

        Returns:
            Dict[str, float]: 质量指标
        """
        self.logger.debug("开始计算质量指标")

        # 网络效率指标
        avg_packet_size = basic_stats.get('average_packet_size', 0)
        efficiency_score = min(100, avg_packet_size / 10)  # 基于包大小的效率评分

        # 连接质量指标
        tcp_packets = [p for p in packets if p.protocol == ProtocolType.TCP]
        udp_packets = [p for p in packets if p.protocol == ProtocolType.UDP]

        protocol_balance = abs(len(tcp_packets) - len(udp_packets)) / len(packets) if packets else 0
        balance_score = (1 - protocol_balance) * 100

        # 时间一致性指标
        timestamps = [p.timestamp for p in packets if p.timestamp]
        if len(timestamps) > 1:
            time_intervals = []
            for i in range(1, len(timestamps)):
                interval = (timestamps[i] - timestamps[i-1]).total_seconds()
                time_intervals.append(interval)

            consistency_score = 100 / (1 + statistics.stdev(time_intervals)) if time_intervals else 0
        else:
            consistency_score = 100

        # 综合质量评分
        overall_quality = (efficiency_score + balance_score + consistency_score) / 3

        return {
            'efficiency_score': efficiency_score,
            'protocol_balance_score': balance_score,
            'time_consistency_score': consistency_score,
            'overall_quality_score': overall_quality
        }
