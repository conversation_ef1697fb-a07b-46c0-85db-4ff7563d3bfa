#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序图生成模块
Time Series Visualization Module

使用Plotly创建交互式的流量时序图表，支持多种时间序列数据的可视化，
包括流量趋势、包速率、连接数变化等。
"""

import logging
from typing import Dict, List, Optional, Any, Tuple, TYPE_CHECKING
from datetime import datetime, timedelta
import pandas as pd

if TYPE_CHECKING:
    import plotly.graph_objects as go

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    # 创建模拟的go模块以避免NameError
    class MockGo:
        class Figure:
            pass
    go = MockGo()
    logging.warning("Plotly库未安装，时序图功能将受限")

from ..core.models import Packet, ProtocolStats, TrafficStats


class TimeSeriesVisualizer:
    """
    时序图可视化器
    
    创建各种类型的时间序列图表，包括流量趋势、协议分布变化、
    连接数变化等，支持交互式操作和多种导出格式。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化时序图可视化器
        
        Args:
            config: 配置参数，包括：
                - theme: 主题样式（默认plotly）
                - width: 图表宽度（默认1200）
                - height: 图表高度（默认600）
                - show_legend: 是否显示图例（默认True）
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.TimeSeriesVisualizer")
        
        # 配置参数
        self.theme = self.config.get('theme', 'plotly')
        self.width = self.config.get('width', 1200)
        self.height = self.config.get('height', 600)
        self.show_legend = self.config.get('show_legend', True)
        
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly库未安装，无法创建时序图")
        else:
            self.logger.info("时序图可视化器初始化成功")
    
    def create_traffic_timeline(self, packets: List[Packet],
                              time_window: str = "1min") -> Optional['go.Figure']:
        """
        创建流量时间线图
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口（1s, 1min, 5min, 1h）
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建流量时间线图")
            return None
        
        if not packets:
            self.logger.warning("数据包列表为空")
            return None
        
        try:
            self.logger.info(f"创建流量时间线图，时间窗口: {time_window}")
            
            # 准备时间序列数据
            df = self._prepare_traffic_data(packets, time_window)
            
            # 创建子图
            fig = make_subplots(
                rows=3, cols=1,
                subplot_titles=('包数量', '字节数', '连接数'),
                vertical_spacing=0.08,
                shared_xaxes=True
            )
            
            # 添加包数量时间线
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=df['packet_count'],
                    mode='lines+markers',
                    name='包数量',
                    line=dict(color='#1f77b4', width=2),
                    marker=dict(size=4),
                    hovertemplate='时间: %{x}<br>包数量: %{y}<extra></extra>'
                ),
                row=1, col=1
            )
            
            # 添加字节数时间线
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=df['byte_count'],
                    mode='lines+markers',
                    name='字节数',
                    line=dict(color='#ff7f0e', width=2),
                    marker=dict(size=4),
                    hovertemplate='时间: %{x}<br>字节数: %{y}<extra></extra>'
                ),
                row=2, col=1
            )
            
            # 添加连接数时间线
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=df['connection_count'],
                    mode='lines+markers',
                    name='连接数',
                    line=dict(color='#2ca02c', width=2),
                    marker=dict(size=4),
                    hovertemplate='时间: %{x}<br>连接数: %{y}<extra></extra>'
                ),
                row=3, col=1
            )
            
            # 更新布局
            fig.update_layout(
                title=f'网络流量时间线分析 (时间窗口: {time_window})',
                width=self.width,
                height=self.height,
                showlegend=self.show_legend,
                template=self.theme,
                hovermode='x unified'
            )
            
            # 更新x轴
            fig.update_xaxes(title_text="时间", row=3, col=1)
            
            # 更新y轴
            fig.update_yaxes(title_text="包数量", row=1, col=1)
            fig.update_yaxes(title_text="字节数", row=2, col=1)
            fig.update_yaxes(title_text="连接数", row=3, col=1)
            
            self.logger.info("流量时间线图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建流量时间线图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_protocol_timeline(self, packets: List[Packet], 
                               time_window: str = "1min") -> Optional[go.Figure]:
        """
        创建协议时间线图
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建协议时间线图")
            return None
        
        if not packets:
            self.logger.warning("数据包列表为空")
            return None
        
        try:
            self.logger.info(f"创建协议时间线图，时间窗口: {time_window}")
            
            # 准备协议时间序列数据
            df = self._prepare_protocol_timeline_data(packets, time_window)
            
            # 创建堆叠面积图
            fig = go.Figure()
            
            # 获取所有协议类型
            protocols = [col for col in df.columns if col != 'timestamp']
            colors = px.colors.qualitative.Set3[:len(protocols)]
            
            # 为每个协议添加面积图
            for i, protocol in enumerate(protocols):
                fig.add_trace(
                    go.Scatter(
                        x=df['timestamp'],
                        y=df[protocol],
                        mode='lines',
                        name=protocol.upper(),
                        stackgroup='one',
                        line=dict(width=0),
                        fillcolor=colors[i % len(colors)],
                        hovertemplate=f'{protocol.upper()}: %{{y}}<br>时间: %{{x}}<extra></extra>'
                    )
                )
            
            # 更新布局
            fig.update_layout(
                title=f'协议分布时间线 (时间窗口: {time_window})',
                xaxis_title='时间',
                yaxis_title='包数量',
                width=self.width,
                height=self.height,
                showlegend=self.show_legend,
                template=self.theme,
                hovermode='x unified'
            )
            
            self.logger.info("协议时间线图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建协议时间线图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_bandwidth_timeline(self, packets: List[Packet], 
                                time_window: str = "1min") -> Optional[go.Figure]:
        """
        创建带宽使用时间线图
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建带宽时间线图")
            return None
        
        if not packets:
            self.logger.warning("数据包列表为空")
            return None
        
        try:
            self.logger.info(f"创建带宽时间线图，时间窗口: {time_window}")
            
            # 准备带宽数据
            df = self._prepare_bandwidth_data(packets, time_window)
            
            # 创建双y轴图表
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # 添加带宽使用（主y轴）
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=df['bandwidth_mbps'],
                    mode='lines+markers',
                    name='带宽使用 (Mbps)',
                    line=dict(color='#1f77b4', width=3),
                    marker=dict(size=6),
                    hovertemplate='时间: %{x}<br>带宽: %{y:.2f} Mbps<extra></extra>'
                ),
                secondary_y=False
            )
            
            # 添加包速率（次y轴）
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=df['packet_rate'],
                    mode='lines+markers',
                    name='包速率 (包/秒)',
                    line=dict(color='#ff7f0e', width=2, dash='dash'),
                    marker=dict(size=4),
                    hovertemplate='时间: %{x}<br>包速率: %{y:.1f} 包/秒<extra></extra>'
                ),
                secondary_y=True
            )
            
            # 设置y轴标题
            fig.update_yaxes(title_text="带宽使用 (Mbps)", secondary_y=False)
            fig.update_yaxes(title_text="包速率 (包/秒)", secondary_y=True)
            
            # 更新布局
            fig.update_layout(
                title=f'网络带宽使用时间线 (时间窗口: {time_window})',
                xaxis_title='时间',
                width=self.width,
                height=self.height,
                showlegend=self.show_legend,
                template=self.theme,
                hovermode='x unified'
            )
            
            self.logger.info("带宽时间线图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建带宽时间线图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def _prepare_traffic_data(self, packets: List[Packet], 
                            time_window: str) -> pd.DataFrame:
        """
        准备流量时间序列数据
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口
            
        Returns:
            pd.DataFrame: 时间序列数据
        """
        # 转换时间窗口为pandas频率
        freq_map = {
            '1s': '1S',
            '1min': '1T',
            '5min': '5T',
            '1h': '1H'
        }
        freq = freq_map.get(time_window, '1T')
        
        # 创建数据框
        data = []
        for packet in packets:
            if packet.timestamp:
                data.append({
                    'timestamp': packet.timestamp,
                    'size': packet.size,
                    'protocol': packet.protocol.value if packet.protocol else 'unknown'
                })
        
        if not data:
            return pd.DataFrame(columns=['timestamp', 'packet_count', 'byte_count', 'connection_count'])
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 按时间窗口聚合
        df_grouped = df.groupby(pd.Grouper(key='timestamp', freq=freq)).agg({
            'size': ['count', 'sum'],
            'protocol': 'nunique'
        }).reset_index()
        
        # 重命名列
        df_grouped.columns = ['timestamp', 'packet_count', 'byte_count', 'connection_count']
        
        return df_grouped
    
    def _prepare_protocol_timeline_data(self, packets: List[Packet], 
                                      time_window: str) -> pd.DataFrame:
        """
        准备协议时间序列数据
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口
            
        Returns:
            pd.DataFrame: 协议时间序列数据
        """
        # 转换时间窗口
        freq_map = {
            '1s': '1S',
            '1min': '1T',
            '5min': '5T',
            '1h': '1H'
        }
        freq = freq_map.get(time_window, '1T')
        
        # 创建数据框
        data = []
        for packet in packets:
            if packet.timestamp:
                data.append({
                    'timestamp': packet.timestamp,
                    'protocol': packet.protocol.value if packet.protocol else 'unknown'
                })
        
        if not data:
            return pd.DataFrame(columns=['timestamp'])
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 创建协议计数
        df['count'] = 1
        
        # 透视表按时间和协议聚合
        df_pivot = df.pivot_table(
            index=pd.Grouper(key='timestamp', freq=freq),
            columns='protocol',
            values='count',
            aggfunc='sum',
            fill_value=0
        ).reset_index()
        
        return df_pivot
    
    def _prepare_bandwidth_data(self, packets: List[Packet], 
                              time_window: str) -> pd.DataFrame:
        """
        准备带宽时间序列数据
        
        Args:
            packets: 数据包列表
            time_window: 时间窗口
            
        Returns:
            pd.DataFrame: 带宽时间序列数据
        """
        # 转换时间窗口
        freq_map = {
            '1s': '1S',
            '1min': '1T',
            '5min': '5T',
            '1h': '1H'
        }
        freq = freq_map.get(time_window, '1T')
        
        # 时间窗口秒数
        window_seconds = {
            '1s': 1,
            '1min': 60,
            '5min': 300,
            '1h': 3600
        }
        seconds = window_seconds.get(time_window, 60)
        
        # 创建数据框
        data = []
        for packet in packets:
            if packet.timestamp:
                data.append({
                    'timestamp': packet.timestamp,
                    'size': packet.size
                })
        
        if not data:
            return pd.DataFrame(columns=['timestamp', 'bandwidth_mbps', 'packet_rate'])
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 按时间窗口聚合
        df_grouped = df.groupby(pd.Grouper(key='timestamp', freq=freq)).agg({
            'size': ['count', 'sum']
        }).reset_index()
        
        # 重命名列
        df_grouped.columns = ['timestamp', 'packet_count', 'byte_count']
        
        # 计算带宽和包速率
        df_grouped['bandwidth_mbps'] = (df_grouped['byte_count'] * 8) / (seconds * 1000000)
        df_grouped['packet_rate'] = df_grouped['packet_count'] / seconds
        
        return df_grouped
    
    def save_figure(self, fig: go.Figure, filename: str, 
                   format: str = "html") -> bool:
        """
        保存图表到文件
        
        Args:
            fig: Plotly图表对象
            filename: 文件名
            format: 保存格式（html, png, pdf, svg）
            
        Returns:
            bool: 是否保存成功
        """
        if not PLOTLY_AVAILABLE or not fig:
            return False
        
        try:
            if format.lower() == "html":
                fig.write_html(filename)
            elif format.lower() == "png":
                fig.write_image(filename, format="png")
            elif format.lower() == "pdf":
                fig.write_image(filename, format="pdf")
            elif format.lower() == "svg":
                fig.write_image(filename, format="svg")
            else:
                self.logger.error(f"不支持的保存格式: {format}")
                return False
            
            self.logger.info(f"图表已保存到: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存图表失败: {str(e)}")
            return False
