#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协议分布图表模块
Protocol Distribution Charts Module

创建协议使用分布的饼图和柱状图可视化，支持多种协议统计的图表展示，
包括协议分布、端口使用、IP地址统计等。
"""

import logging
from typing import Dict, List, Optional, Any, Tuple, TYPE_CHECKING
import pandas as pd

if TYPE_CHECKING:
    import plotly.graph_objects as go

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    # 创建模拟的go模块以避免NameError
    class MockGo:
        class Figure:
            pass
    go = MockGo()
    logging.warning("Plotly库未安装，协议分布图功能将受限")

from ..core.models import ProtocolStats


class ProtocolChartVisualizer:
    """
    协议图表可视化器
    
    创建各种协议相关的图表，包括协议分布饼图、柱状图、
    端口使用统计、IP地址分布等可视化图表。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化协议图表可视化器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.ProtocolChartVisualizer")
        
        # 配置参数
        self.width = self.config.get('width', 1000)
        self.height = self.config.get('height', 600)
        self.theme = self.config.get('theme', 'plotly')
        self.color_scheme = self.config.get('color_scheme', 'Set3')
        
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly库未安装，无法创建协议分布图")
        else:
            self.logger.info("协议图表可视化器初始化成功")
    
    def create_protocol_pie_chart(self, protocol_stats: ProtocolStats) -> Optional['go.Figure']:
        """
        创建协议分布饼图
        
        Args:
            protocol_stats: 协议统计数据
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建协议饼图")
            return None
        
        if not protocol_stats.protocol_counts:
            self.logger.warning("协议统计数据为空")
            return None
        
        try:
            self.logger.info("创建协议分布饼图")
            
            # 准备数据
            protocols = list(protocol_stats.protocol_counts.keys())
            counts = list(protocol_stats.protocol_counts.values())
            percentages = [protocol_stats.protocol_percentages.get(p, 0) for p in protocols]
            
            # 创建饼图
            fig = go.Figure(data=[
                go.Pie(
                    labels=[p.upper() for p in protocols],
                    values=counts,
                    hole=0.3,  # 创建环形图
                    textinfo='label+percent',
                    textposition='auto',
                    hovertemplate='<b>%{label}</b><br>' +
                                '包数量: %{value:,}<br>' +
                                '占比: %{percent}<br>' +
                                '<extra></extra>',
                    marker=dict(
                        colors=px.colors.qualitative.Set3[:len(protocols)],
                        line=dict(color='#FFFFFF', width=2)
                    )
                )
            ])
            
            # 更新布局
            fig.update_layout(
                title=f'网络协议分布 (总包数: {protocol_stats.total_packets:,})',
                width=self.width,
                height=self.height,
                template=self.theme,
                showlegend=True,
                legend=dict(
                    orientation="v",
                    yanchor="middle",
                    y=0.5,
                    xanchor="left",
                    x=1.01
                ),
                annotations=[
                    dict(
                        text=f'总计<br>{protocol_stats.total_packets:,}包',
                        x=0.5, y=0.5,
                        font_size=16,
                        showarrow=False
                    )
                ]
            )
            
            self.logger.info("协议分布饼图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建协议饼图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_protocol_bar_chart(self, protocol_stats: ProtocolStats) -> Optional['go.Figure']:
        """
        创建协议分布柱状图

        Args:
            protocol_stats: 协议统计数据

        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建协议柱状图")
            return None
        
        if not protocol_stats.protocol_counts:
            self.logger.warning("协议统计数据为空")
            return None
        
        try:
            self.logger.info("创建协议分布柱状图")
            
            # 准备数据并排序
            protocol_data = [
                (protocol, count, protocol_stats.protocol_bytes.get(protocol, 0))
                for protocol, count in protocol_stats.protocol_counts.items()
            ]
            protocol_data.sort(key=lambda x: x[1], reverse=True)
            
            protocols = [item[0].upper() for item in protocol_data]
            packet_counts = [item[1] for item in protocol_data]
            byte_counts = [item[2] for item in protocol_data]
            
            # 创建双y轴柱状图
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # 添加包数量柱状图
            fig.add_trace(
                go.Bar(
                    x=protocols,
                    y=packet_counts,
                    name='包数量',
                    marker_color='lightblue',
                    hovertemplate='协议: %{x}<br>包数量: %{y:,}<extra></extra>',
                    yaxis='y'
                ),
                secondary_y=False
            )
            
            # 添加字节数柱状图
            fig.add_trace(
                go.Bar(
                    x=protocols,
                    y=byte_counts,
                    name='字节数',
                    marker_color='lightcoral',
                    opacity=0.7,
                    hovertemplate='协议: %{x}<br>字节数: %{y:,}<extra></extra>',
                    yaxis='y2'
                ),
                secondary_y=True
            )
            
            # 设置y轴标题
            fig.update_yaxes(title_text="包数量", secondary_y=False)
            fig.update_yaxes(title_text="字节数", secondary_y=True)
            
            # 更新布局
            fig.update_layout(
                title='协议使用统计 (包数量 vs 字节数)',
                xaxis_title='协议类型',
                width=self.width,
                height=self.height,
                template=self.theme,
                showlegend=True,
                barmode='group'
            )
            
            self.logger.info("协议分布柱状图创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建协议柱状图失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_port_usage_chart(self, protocol_stats: ProtocolStats, 
                              chart_type: str = "bar") -> Optional[go.Figure]:
        """
        创建端口使用图表
        
        Args:
            protocol_stats: 协议统计数据
            chart_type: 图表类型（bar, horizontal_bar）
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建端口使用图表")
            return None
        
        try:
            self.logger.info(f"创建端口使用图表，类型: {chart_type}")
            
            # 创建子图
            fig = make_subplots(
                rows=1, cols=2,
                subplot_titles=('Top源端口', 'Top目标端口'),
                horizontal_spacing=0.1
            )
            
            # 源端口数据
            if protocol_stats.top_src_ports:
                src_ports = [str(port) for port, count in protocol_stats.top_src_ports]
                src_counts = [count for port, count in protocol_stats.top_src_ports]
                
                if chart_type == "horizontal_bar":
                    fig.add_trace(
                        go.Bar(
                            y=src_ports,
                            x=src_counts,
                            name='源端口',
                            orientation='h',
                            marker_color='lightblue',
                            hovertemplate='端口: %{y}<br>使用次数: %{x:,}<extra></extra>'
                        ),
                        row=1, col=1
                    )
                else:
                    fig.add_trace(
                        go.Bar(
                            x=src_ports,
                            y=src_counts,
                            name='源端口',
                            marker_color='lightblue',
                            hovertemplate='端口: %{x}<br>使用次数: %{y:,}<extra></extra>'
                        ),
                        row=1, col=1
                    )
            
            # 目标端口数据
            if protocol_stats.top_dst_ports:
                dst_ports = [str(port) for port, count in protocol_stats.top_dst_ports]
                dst_counts = [count for port, count in protocol_stats.top_dst_ports]
                
                if chart_type == "horizontal_bar":
                    fig.add_trace(
                        go.Bar(
                            y=dst_ports,
                            x=dst_counts,
                            name='目标端口',
                            orientation='h',
                            marker_color='lightcoral',
                            hovertemplate='端口: %{y}<br>使用次数: %{x:,}<extra></extra>'
                        ),
                        row=1, col=2
                    )
                else:
                    fig.add_trace(
                        go.Bar(
                            x=dst_ports,
                            y=dst_counts,
                            name='目标端口',
                            marker_color='lightcoral',
                            hovertemplate='端口: %{x}<br>使用次数: %{y:,}<extra></extra>'
                        ),
                        row=1, col=2
                    )
            
            # 更新布局
            fig.update_layout(
                title='端口使用统计',
                width=self.width,
                height=self.height,
                template=self.theme,
                showlegend=False
            )
            
            # 更新轴标签
            if chart_type == "horizontal_bar":
                fig.update_xaxes(title_text="使用次数")
                fig.update_yaxes(title_text="端口号")
            else:
                fig.update_xaxes(title_text="端口号")
                fig.update_yaxes(title_text="使用次数")
            
            self.logger.info("端口使用图表创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建端口使用图表失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_ip_distribution_chart(self, protocol_stats: ProtocolStats) -> Optional[go.Figure]:
        """
        创建IP地址分布图表
        
        Args:
            protocol_stats: 协议统计数据
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建IP分布图表")
            return None
        
        try:
            self.logger.info("创建IP地址分布图表")
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=('Top源IP地址', 'Top目标IP地址'),
                vertical_spacing=0.15
            )
            
            # 源IP数据
            if protocol_stats.top_src_ips:
                src_ips = [ip for ip, count in protocol_stats.top_src_ips]
                src_counts = [count for ip, count in protocol_stats.top_src_ips]
                
                fig.add_trace(
                    go.Bar(
                        x=src_ips,
                        y=src_counts,
                        name='源IP',
                        marker_color='lightgreen',
                        hovertemplate='IP: %{x}<br>包数量: %{y:,}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
            # 目标IP数据
            if protocol_stats.top_dst_ips:
                dst_ips = [ip for ip, count in protocol_stats.top_dst_ips]
                dst_counts = [count for ip, count in protocol_stats.top_dst_ips]
                
                fig.add_trace(
                    go.Bar(
                        x=dst_ips,
                        y=dst_counts,
                        name='目标IP',
                        marker_color='lightsalmon',
                        hovertemplate='IP: %{x}<br>包数量: %{y:,}<extra></extra>'
                    ),
                    row=2, col=1
                )
            
            # 更新布局
            fig.update_layout(
                title='IP地址使用分布',
                width=self.width,
                height=self.height,
                template=self.theme,
                showlegend=False
            )
            
            # 更新轴标签
            fig.update_xaxes(title_text="IP地址", row=2, col=1)
            fig.update_yaxes(title_text="包数量")
            
            self.logger.info("IP地址分布图表创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建IP分布图表失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_protocol_comparison_chart(self, protocol_stats: ProtocolStats) -> Optional[go.Figure]:
        """
        创建协议对比图表
        
        Args:
            protocol_stats: 协议统计数据
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建协议对比图表")
            return None
        
        try:
            self.logger.info("创建协议对比图表")
            
            # 准备数据
            protocols = list(protocol_stats.protocol_counts.keys())
            packet_counts = list(protocol_stats.protocol_counts.values())
            byte_counts = [protocol_stats.protocol_bytes.get(p, 0) for p in protocols]
            percentages = [protocol_stats.protocol_percentages.get(p, 0) for p in protocols]
            
            # 创建雷达图
            fig = go.Figure()
            
            # 标准化数据（0-100范围）
            max_packets = max(packet_counts) if packet_counts else 1
            max_bytes = max(byte_counts) if byte_counts else 1
            
            normalized_packets = [(count / max_packets) * 100 for count in packet_counts]
            normalized_bytes = [(count / max_bytes) * 100 for count in byte_counts]
            
            # 添加包数量雷达图
            fig.add_trace(go.Scatterpolar(
                r=normalized_packets,
                theta=[p.upper() for p in protocols],
                fill='toself',
                name='包数量 (标准化)',
                line_color='blue',
                fillcolor='rgba(0, 0, 255, 0.1)'
            ))
            
            # 添加字节数雷达图
            fig.add_trace(go.Scatterpolar(
                r=normalized_bytes,
                theta=[p.upper() for p in protocols],
                fill='toself',
                name='字节数 (标准化)',
                line_color='red',
                fillcolor='rgba(255, 0, 0, 0.1)'
            ))
            
            # 更新布局
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )
                ),
                title='协议使用对比 (雷达图)',
                width=self.width,
                height=self.height,
                template=self.theme,
                showlegend=True
            )
            
            self.logger.info("协议对比图表创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建协议对比图表失败: {str(e)}"
            self.logger.error(error_msg)
            return None
    
    def create_summary_dashboard(self, protocol_stats: ProtocolStats) -> Optional[go.Figure]:
        """
        创建协议统计仪表板
        
        Args:
            protocol_stats: 协议统计数据
            
        Returns:
            Optional[go.Figure]: Plotly图表对象
        """
        if not PLOTLY_AVAILABLE:
            self.logger.error("Plotly不可用，无法创建协议仪表板")
            return None
        
        try:
            self.logger.info("创建协议统计仪表板")
            
            # 创建2x2子图
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('协议分布', '端口使用', 'IP分布', '统计摘要'),
                specs=[[{"type": "pie"}, {"type": "bar"}],
                       [{"type": "bar"}, {"type": "table"}]],
                horizontal_spacing=0.1,
                vertical_spacing=0.15
            )
            
            # 1. 协议分布饼图
            if protocol_stats.protocol_counts:
                protocols = list(protocol_stats.protocol_counts.keys())
                counts = list(protocol_stats.protocol_counts.values())
                
                fig.add_trace(
                    go.Pie(
                        labels=[p.upper() for p in protocols],
                        values=counts,
                        name="协议分布",
                        textinfo='label+percent',
                        textposition='auto'
                    ),
                    row=1, col=1
                )
            
            # 2. 端口使用柱状图
            if protocol_stats.top_dst_ports:
                ports = [str(port) for port, count in protocol_stats.top_dst_ports[:5]]
                port_counts = [count for port, count in protocol_stats.top_dst_ports[:5]]
                
                fig.add_trace(
                    go.Bar(
                        x=ports,
                        y=port_counts,
                        name="目标端口",
                        marker_color='lightcoral'
                    ),
                    row=1, col=2
                )
            
            # 3. IP分布柱状图
            if protocol_stats.top_src_ips:
                ips = [ip.split('.')[-1] for ip, count in protocol_stats.top_src_ips[:5]]  # 只显示最后一段
                ip_counts = [count for ip, count in protocol_stats.top_src_ips[:5]]
                
                fig.add_trace(
                    go.Bar(
                        x=ips,
                        y=ip_counts,
                        name="源IP",
                        marker_color='lightgreen'
                    ),
                    row=2, col=1
                )
            
            # 4. 统计摘要表格
            summary_data = [
                ['总包数', f'{protocol_stats.total_packets:,}'],
                ['总字节数', f'{protocol_stats.total_bytes:,}'],
                ['唯一流数', f'{protocol_stats.unique_flows:,}'],
                ['协议种类', f'{len(protocol_stats.protocol_counts)}'],
                ['分析时长', f'{protocol_stats.duration_seconds:.1f}秒']
            ]
            
            fig.add_trace(
                go.Table(
                    header=dict(values=['指标', '数值'],
                               fill_color='lightblue',
                               align='left'),
                    cells=dict(values=list(zip(*summary_data)),
                              fill_color='white',
                              align='left')
                ),
                row=2, col=2
            )
            
            # 更新布局
            fig.update_layout(
                title='网络协议分析仪表板',
                width=self.width * 1.5,
                height=self.height * 1.2,
                template=self.theme,
                showlegend=False
            )
            
            self.logger.info("协议统计仪表板创建成功")
            return fig
            
        except Exception as e:
            error_msg = f"创建协议仪表板失败: {str(e)}"
            self.logger.error(error_msg)
            return None
