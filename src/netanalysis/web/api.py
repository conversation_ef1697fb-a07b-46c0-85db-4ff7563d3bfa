#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI应用程序
FastAPI Application

完成FastAPI框架的中间件、认证、文档等功能，
提供完整的Web API接口。
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import os
import hashlib
import uuid
from pathlib import Path

try:
    from fastapi import FastAPI, HTTPException, Depends, UploadFile, File, Form
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
    from pydantic import BaseModel
    from sqlalchemy.orm import Session
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logging.warning("FastAPI库未安装，Web API功能将受限")

from ..core.analyzer import NetworkAnalyzer, AnalysisResult
from ..core.models import Packet, ProtocolType, PacketDirection
from ..ai import ContextAwareAnalyzer, AnalysisContext, NetworkEnvironment, BusinessContext
from ..visualization import (
    TimeSeriesVisualizer, ProtocolChartVisualizer, 
    NetworkTopologyVisualizer, InteractiveChartManager
)


# Pydantic模型
class AnalysisRequest(BaseModel):
    """分析请求模型"""
    analysis_type: str = "comprehensive"
    enable_ai: bool = False
    enable_visualization: bool = True
    context: Optional[Dict[str, Any]] = None


class AnalysisResponse(BaseModel):
    """分析响应模型"""
    success: bool
    analysis_id: str
    timestamp: str
    results: Dict[str, Any]
    visualizations: Optional[Dict[str, str]] = None
    ai_insights: Optional[str] = None
    error_message: Optional[str] = None


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: str
    version: str
    dependencies: Dict[str, bool]


# 全局变量
app = None
security = HTTPBearer() if FASTAPI_AVAILABLE else None


def create_app(config: Optional[Dict[str, Any]] = None) -> 'FastAPI':
    """
    创建FastAPI应用程序
    
    Args:
        config: 应用配置
        
    Returns:
        FastAPI: FastAPI应用实例
    """
    if not FASTAPI_AVAILABLE:
        raise ImportError("FastAPI库未安装，无法创建Web应用")
    
    # 应用配置
    app_config = config or {}
    debug = app_config.get('debug', False)
    title = app_config.get('title', '网络数据包分析工具')
    description = app_config.get('description', '基于AI的网络数据包分析和可视化平台')
    version = app_config.get('version', '1.0.0')
    
    # 创建FastAPI应用
    app = FastAPI(
        title=title,
        description=description,
        version=version,
        debug=debug,
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 设置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 初始化组件
    analyzer = NetworkAnalyzer()
    ai_analyzer = None
    visualizers = {
        'time_series': TimeSeriesVisualizer(),
        'protocol_charts': ProtocolChartVisualizer(),
        'network_topology': NetworkTopologyVisualizer(),
        'interactive': InteractiveChartManager()
    }
    
    # 尝试初始化AI分析器
    try:
        ai_config = app_config.get('ai', {'provider': 'mock'})
        ai_analyzer = ContextAwareAnalyzer(ai_config)
    except Exception as e:
        logging.warning(f"AI分析器初始化失败: {e}")
    
    @app.get("/", response_class=HTMLResponse)
    async def root():
        """根路径，返回欢迎页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>网络数据包分析工具</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { text-align: center; color: #333; }
                .nav { margin: 20px 0; }
                .nav a { margin: 0 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
                .nav a:hover { background: #0056b3; }
                .feature { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 网络数据包分析工具</h1>
                <p>基于AI的网络数据包分析和可视化平台</p>
            </div>
            
            <div class="nav">
                <a href="/docs">API文档</a>
                <a href="/health">健康检查</a>
                <a href="/upload">文件上传</a>
            </div>
            
            <div class="feature">
                <h3>🔍 核心功能</h3>
                <ul>
                    <li>协议统计分析：TCP、UDP、ICMP等协议的详细统计</li>
                    <li>流量时序分析：带宽使用、包速率的时间序列分析</li>
                    <li>异常检测：基于规则和统计的网络异常识别</li>
                    <li>安全威胁检测：DDoS、端口扫描等攻击检测</li>
                    <li>AI智能分析：基于大语言模型的智能网络分析</li>
                    <li>交互式可视化：时序图、拓扑图、协议分布图</li>
                </ul>
            </div>
            
            <div class="feature">
                <h3>📊 可视化功能</h3>
                <ul>
                    <li>流量时间线图表</li>
                    <li>协议分布饼图和柱状图</li>
                    <li>网络拓扑图</li>
                    <li>交互式仪表板</li>
                </ul>
            </div>
        </body>
        </html>
        """
    
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查接口"""
        from ..models import get_database_manager

        # 检查各种依赖和服务状态
        dependencies = {
            "fastapi": FASTAPI_AVAILABLE,
            "plotly": True,  # 从visualization模块检查
            "networkx": True,  # 从visualization模块检查
            "ai_available": ai_analyzer is not None
        }

        # 检查数据库连接
        try:
            db_manager = get_database_manager()
            db_health = db_manager.health_check()
            dependencies["database"] = db_health["status"] == "healthy"
            dependencies["database_details"] = db_health
        except Exception as e:
            dependencies["database"] = False
            dependencies["database_error"] = str(e)

        # 检查磁盘空间（简单检查）
        try:
            import shutil
            disk_usage = shutil.disk_usage(".")
            free_gb = disk_usage.free / (1024**3)
            dependencies["disk_space"] = free_gb > 1.0  # 至少1GB可用空间
            dependencies["disk_free_gb"] = round(free_gb, 2)
        except Exception as e:
            dependencies["disk_space"] = False
            dependencies["disk_error"] = str(e)

        # 检查内存使用情况
        try:
            import psutil
            memory = psutil.virtual_memory()
            dependencies["memory"] = memory.percent < 90  # 内存使用率低于90%
            dependencies["memory_usage_percent"] = memory.percent
            dependencies["memory_available_gb"] = round(memory.available / (1024**3), 2)
        except ImportError:
            dependencies["memory"] = True  # 如果psutil不可用，假设正常
        except Exception as e:
            dependencies["memory"] = False
            dependencies["memory_error"] = str(e)

        # 计算整体健康状态
        critical_checks = ["fastapi", "database"]
        critical_status = all(dependencies.get(check, False) for check in critical_checks)
        overall_status = "healthy" if critical_status else "unhealthy"

        # 如果关键服务正常但其他服务有问题，标记为降级
        if critical_status and not all(dependencies.get(k, True) for k in dependencies if k not in critical_checks and not k.endswith("_details") and not k.endswith("_error") and not k.endswith("_gb") and not k.endswith("_percent")):
            overall_status = "degraded"

        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now().isoformat(),
            version="1.0.0",
            dependencies=dependencies
        )
    
    # 数据库依赖注入
    def get_db():
        """获取数据库会话"""
        from ..models import get_db_session
        return next(get_db_session())

    @app.post("/api/v1/files/upload")
    async def upload_file_v2(
        file: UploadFile = File(...),
        description: Optional[str] = Form(None),
        db: Session = Depends(get_db)
    ):
        """
        文件上传API v2 - 集成数据库存储

        支持PCAP、PCAPNG等网络包格式的文件上传，
        并将文件信息存储到数据库中。
        """
        from ..models import UploadedFile, FileFormat
        from ..core.utils import detect_file_format, calculate_file_hash

        try:
            # 验证文件类型
            allowed_extensions = {'.pcap', '.pcapng', '.cap', '.dmp'}
            file_extension = Path(file.filename or "unknown").suffix.lower()

            if file_extension not in allowed_extensions:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}"
                )

            # 读取文件内容
            content = await file.read()

            # 验证文件大小 (最大100MB)
            max_size = 100 * 1024 * 1024  # 100MB
            if len(content) > max_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大。最大支持 {max_size // 1024 // 1024} MB"
                )

            # 计算文件哈希
            file_hash = hashlib.sha256(content).hexdigest()

            # 检查文件是否已存在
            existing_file = db.query(UploadedFile).filter(
                UploadedFile.file_hash == file_hash,
                UploadedFile.is_deleted == False
            ).first()

            if existing_file:
                return {
                    "success": True,
                    "file_id": existing_file.id,
                    "filename": existing_file.filename,
                    "size": existing_file.file_size,
                    "message": "文件已存在，返回现有文件信息",
                    "existing": True
                }

            # 生成唯一文件名和路径
            file_id = str(uuid.uuid4())
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_filename = f"{timestamp}_{file_id[:8]}_{file.filename}"

            # 创建上传目录
            upload_dir = Path("data/uploads")
            upload_dir.mkdir(parents=True, exist_ok=True)
            file_path = upload_dir / safe_filename

            # 保存文件
            with open(file_path, "wb") as f:
                f.write(content)

            # 检测文件格式
            try:
                detected_format = detect_file_format(str(file_path))
                file_format = FileFormat(detected_format)
            except:
                # 根据扩展名推断格式
                format_map = {
                    '.pcap': FileFormat.PCAP,
                    '.pcapng': FileFormat.PCAPNG,
                    '.cap': FileFormat.CAP,
                    '.dmp': FileFormat.PCAP
                }
                file_format = format_map.get(file_extension, FileFormat.UNKNOWN)

            # 创建数据库记录
            db_file = UploadedFile(
                id=file_id,
                filename=safe_filename,
                original_filename=file.filename or "unknown",
                file_path=str(file_path),
                file_size=len(content),
                file_hash=file_hash,
                file_format=file_format,
                mime_type=file.content_type or "application/octet-stream",
                user_id="system",  # 临时用户ID，后续集成认证后修改
            )

            db.add(db_file)
            db.commit()
            db.refresh(db_file)

            return {
                "success": True,
                "file_id": db_file.id,
                "filename": db_file.filename,
                "original_filename": db_file.original_filename,
                "size": db_file.file_size,
                "format": db_file.file_format.value,
                "hash": db_file.file_hash,
                "description": description,
                "upload_time": db_file.uploaded_at.isoformat(),
                "message": "文件上传成功"
            }

        except HTTPException:
            raise
        except Exception as e:
            # 清理可能创建的文件
            if 'file_path' in locals() and Path(file_path).exists():
                Path(file_path).unlink()

            logging.error(f"文件上传失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"文件上传失败: {str(e)}"
            )

    @app.post("/upload", response_model=Dict[str, Any])
    async def upload_file(
        file: UploadFile = File(...),
        description: Optional[str] = Form(None)
    ):
        """
        安全的文件上传接口（兼容性版本）

        支持PCAP、PCAPNG等网络包格式的文件上传
        """
        try:
            # 验证文件类型
            allowed_extensions = {'.pcap', '.pcapng', '.cap', '.dmp'}
            file_extension = Path(file.filename or "unknown").suffix.lower()

            if file_extension not in allowed_extensions:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}"
                )

            # 验证文件大小 (最大100MB)
            max_size = 100 * 1024 * 1024  # 100MB
            content = await file.read()

            if len(content) > max_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大。最大支持 {max_size // 1024 // 1024} MB"
                )

            # 生成唯一文件ID
            file_id = f"upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"

            # 保存文件到临时目录
            upload_dir = Path("uploads")
            upload_dir.mkdir(exist_ok=True)
            file_path = upload_dir / file_id

            with open(file_path, "wb") as f:
                f.write(content)

            return {
                "success": True,
                "file_id": file_id,
                "filename": file.filename,
                "size": len(content),
                "description": description,
                "upload_time": datetime.now().isoformat(),
                "message": "文件上传成功"
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"文件上传失败: {str(e)}"
            )

    @app.post("/api/v1/analysis/create")
    async def create_analysis_task(
        task_data: Dict[str, Any],
        db: Session = Depends(get_db)
    ):
        """
        创建分析任务API

        创建新的网络数据包分析任务
        """
        from ..models import AnalysisTask, UploadedFile, TaskStatus

        try:
            # 验证必需字段
            required_fields = ["name", "uploaded_file_id"]
            for field in required_fields:
                if field not in task_data:
                    raise HTTPException(
                        status_code=400,
                        detail=f"缺少必需字段: {field}"
                    )

            # 验证上传文件是否存在
            uploaded_file = db.query(UploadedFile).filter(
                UploadedFile.id == task_data["uploaded_file_id"],
                UploadedFile.is_deleted == False
            ).first()

            if not uploaded_file:
                raise HTTPException(
                    status_code=404,
                    detail="指定的文件不存在"
                )

            # 创建分析任务
            task = AnalysisTask(
                id=str(uuid.uuid4()),
                name=task_data["name"],
                description=task_data.get("description"),
                uploaded_file_id=task_data["uploaded_file_id"],
                user_id="system",  # 临时用户ID
                enable_ai_analysis=task_data.get("enable_ai_analysis", False),
                enable_visualization=task_data.get("enable_visualization", True),
                enable_anomaly_detection=task_data.get("enable_anomaly_detection", True),
                analysis_config=task_data.get("analysis_config", {}),
                status=TaskStatus.PENDING
            )

            db.add(task)
            db.commit()
            db.refresh(task)

            return {
                "success": True,
                "task_id": task.id,
                "name": task.name,
                "status": task.status.value,
                "created_at": task.created_at.isoformat(),
                "message": "分析任务创建成功"
            }

        except HTTPException:
            raise
        except Exception as e:
            logging.error(f"创建分析任务失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"创建分析任务失败: {str(e)}"
            )

    @app.get("/api/v1/analysis/{task_id}")
    async def get_analysis_task(
        task_id: str,
        db: Session = Depends(get_db)
    ):
        """
        获取分析任务详情API
        """
        from ..models import AnalysisTask

        try:
            task = db.query(AnalysisTask).filter(AnalysisTask.id == task_id).first()

            if not task:
                raise HTTPException(
                    status_code=404,
                    detail="分析任务不存在"
                )

            return {
                "success": True,
                "task": {
                    "id": task.id,
                    "name": task.name,
                    "description": task.description,
                    "status": task.status.value,
                    "progress": task.progress,
                    "uploaded_file_id": task.uploaded_file_id,
                    "enable_ai_analysis": task.enable_ai_analysis,
                    "enable_visualization": task.enable_visualization,
                    "enable_anomaly_detection": task.enable_anomaly_detection,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "processing_time": task.processing_time,
                    "memory_usage": task.memory_usage,
                    "error_message": task.error_message,
                    "result_data": task.result_data
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            logging.error(f"获取分析任务失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"获取分析任务失败: {str(e)}"
            )

    @app.get("/api/v1/analysis")
    async def list_analysis_tasks(
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None,
        db: Session = Depends(get_db)
    ):
        """
        获取分析任务列表API
        """
        from ..models import AnalysisTask, TaskStatus

        try:
            query = db.query(AnalysisTask)

            # 按状态过滤
            if status:
                try:
                    status_enum = TaskStatus(status)
                    query = query.filter(AnalysisTask.status == status_enum)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"无效的状态值: {status}"
                    )

            # 分页
            total = query.count()
            tasks = query.order_by(AnalysisTask.created_at.desc()).offset(skip).limit(limit).all()

            task_list = []
            for task in tasks:
                task_list.append({
                    "id": task.id,
                    "name": task.name,
                    "status": task.status.value,
                    "progress": task.progress,
                    "uploaded_file_id": task.uploaded_file_id,
                    "created_at": task.created_at.isoformat(),
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                })

            return {
                "success": True,
                "tasks": task_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except HTTPException:
            raise
        except Exception as e:
            logging.error(f"获取分析任务列表失败: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"获取分析任务列表失败: {str(e)}"
            )

    @app.post("/analyze", response_model=AnalysisResponse)
    async def analyze_packets(
        file: UploadFile = File(...),
        request: AnalysisRequest = Depends()
    ):
        """
        分析上传的数据包文件
        
        Args:
            file: 上传的文件
            request: 分析请求参数
            
        Returns:
            AnalysisResponse: 分析结果
        """
        try:
            # 生成分析ID
            analysis_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 读取文件内容（这里简化处理，实际应该解析PCAP文件）
            content = await file.read()
            
            # 创建模拟数据包（实际应该从PCAP文件解析）
            packets = _create_demo_packets()
            
            # 执行基础分析
            result = AnalysisResult(analysis_id)
            result.protocol_stats = analyzer.analyze_protocols(packets)
            result.traffic_stats = analyzer.analyze_traffic(packets)
            result.anomalies = analyzer.detect_anomalies(packets)
            result.security_threats = analyzer.detect_security_threats(packets)
            
            # AI分析（如果启用）
            ai_insights = None
            if request.enable_ai and ai_analyzer:
                try:
                    context = AnalysisContext()
                    if request.context:
                        # 从请求中设置上下文
                        context.network_environment = NetworkEnvironment(
                            request.context.get('environment', 'unknown')
                        )
                        context.business_context = BusinessContext(
                            request.context.get('business', 'general')
                        )
                    
                    ai_result = ai_analyzer.analyze_with_context(
                        result.to_dict(), context
                    )
                    ai_insights = ai_result.get('ai_analysis', '')
                except Exception as e:
                    logging.warning(f"AI分析失败: {e}")
            
            # 生成可视化（如果启用）
            visualizations = {}
            if request.enable_visualization:
                try:
                    # 时序图
                    time_fig = visualizers['time_series'].create_traffic_timeline(packets)
                    if time_fig:
                        time_html = time_fig.to_html(include_plotlyjs='cdn')
                        visualizations['timeline'] = time_html
                    
                    # 协议分布图
                    protocol_fig = visualizers['protocol_charts'].create_protocol_pie_chart(
                        result.protocol_stats
                    )
                    if protocol_fig:
                        protocol_html = protocol_fig.to_html(include_plotlyjs='cdn')
                        visualizations['protocol_distribution'] = protocol_html
                    
                except Exception as e:
                    logging.warning(f"可视化生成失败: {e}")
            
            return AnalysisResponse(
                success=True,
                analysis_id=analysis_id,
                timestamp=datetime.now().isoformat(),
                results=result.to_dict(),
                visualizations=visualizations,
                ai_insights=ai_insights
            )
            
        except Exception as e:
            error_msg = f"分析失败: {str(e)}"
            logging.error(error_msg)
            return AnalysisResponse(
                success=False,
                analysis_id="",
                timestamp=datetime.now().isoformat(),
                results={},
                error_message=error_msg
            )
    
    @app.get("/upload", response_class=HTMLResponse)
    async def upload_page():
        """文件上传页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>文件上传 - 网络数据包分析工具</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 600px; margin: 0 auto; }
                .upload-form { border: 2px dashed #ccc; padding: 40px; text-align: center; border-radius: 10px; }
                .upload-form:hover { border-color: #007bff; }
                input[type="file"] { margin: 20px 0; }
                button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
                button:hover { background: #0056b3; }
                .options { margin: 20px 0; text-align: left; }
                .options label { display: block; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📁 文件上传</h1>
                <form class="upload-form" action="/analyze" method="post" enctype="multipart/form-data">
                    <h3>选择网络数据包文件</h3>
                    <p>支持格式：PCAP, PCAPNG</p>
                    <input type="file" name="file" accept=".pcap,.pcapng" required>
                    
                    <div class="options">
                        <label>
                            <input type="checkbox" name="enable_ai" value="true"> 启用AI分析
                        </label>
                        <label>
                            <input type="checkbox" name="enable_visualization" value="true" checked> 生成可视化图表
                        </label>
                    </div>
                    
                    <button type="submit">开始分析</button>
                </form>
            </div>
        </body>
        </html>
        """

    @app.get("/files", response_model=Dict[str, Any])
    async def list_uploaded_files():
        """
        列出已上传的文件
        """
        try:
            upload_dir = Path("uploads")
            if not upload_dir.exists():
                return {"files": [], "total": 0}

            files = []
            for file_path in upload_dir.glob("*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    files.append({
                        "file_id": file_path.name,
                        "filename": file_path.name.split("_", 3)[-1] if "_" in file_path.name else file_path.name,
                        "size": stat.st_size,
                        "upload_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })

            # 按上传时间排序
            files.sort(key=lambda x: x["upload_time"], reverse=True)

            return {
                "files": files,
                "total": len(files),
                "upload_dir": str(upload_dir.absolute())
            }

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"获取文件列表失败: {str(e)}"
            )

    @app.delete("/files/{file_id}")
    async def delete_uploaded_file(file_id: str):
        """
        删除已上传的文件
        """
        try:
            upload_dir = Path("uploads")
            file_path = upload_dir / file_id

            if not file_path.exists():
                raise HTTPException(
                    status_code=404,
                    detail="文件不存在"
                )

            file_path.unlink()

            return {
                "success": True,
                "message": f"文件 {file_id} 已删除"
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"删除文件失败: {str(e)}"
            )

    @app.get("/results/{analysis_id}", response_model=Dict[str, Any])
    async def get_analysis_results(analysis_id: str):
        """
        获取分析结果详情
        """
        try:
            # 这里应该从数据库或文件系统获取结果
            # 目前使用模拟数据
            results_dir = Path("results")
            result_file = results_dir / f"{analysis_id}.json"

            if not result_file.exists():
                raise HTTPException(
                    status_code=404,
                    detail="分析结果不存在"
                )

            with open(result_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            return {
                "success": True,
                "analysis_id": analysis_id,
                "results": results,
                "retrieved_at": datetime.now().isoformat()
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"获取分析结果失败: {str(e)}"
            )

    @app.get("/results", response_model=Dict[str, Any])
    async def list_analysis_results():
        """
        列出所有分析结果
        """
        try:
            results_dir = Path("results")
            if not results_dir.exists():
                return {"results": [], "total": 0}

            results = []
            for result_file in results_dir.glob("*.json"):
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    stat = result_file.stat()
                    results.append({
                        "analysis_id": result_file.stem,
                        "timestamp": data.get("timestamp", ""),
                        "total_packets": data.get("protocol_stats", {}).get("total_packets", 0),
                        "file_size": stat.st_size,
                        "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat()
                    })
                except Exception:
                    continue

            # 按创建时间排序
            results.sort(key=lambda x: x["created_at"], reverse=True)

            return {
                "results": results,
                "total": len(results)
            }

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"获取结果列表失败: {str(e)}"
            )

    @app.get("/dashboard", response_class=HTMLResponse)
    async def analysis_dashboard():
        """
        分析结果仪表板页面
        """
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>分析结果仪表板 - 网络数据包分析工具</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: #f5f5f5;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    overflow: hidden;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }
                .nav {
                    background: #f8f9fa;
                    padding: 15px 30px;
                    border-bottom: 1px solid #dee2e6;
                }
                .nav a {
                    margin: 0 15px;
                    padding: 8px 16px;
                    background: #007bff;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                    transition: background 0.3s;
                }
                .nav a:hover { background: #0056b3; }
                .content { padding: 30px; }
                .card {
                    background: white;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px 0;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                .grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }
                .stat-card {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                    border-radius: 8px;
                    padding: 20px;
                }
                .stat-number { font-size: 2em; font-weight: bold; }
                .stat-label { opacity: 0.9; margin-top: 5px; }
                .loading { text-align: center; padding: 40px; color: #666; }
                .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .success { color: #155724; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
                th { background: #f8f9fa; font-weight: 600; }
                .btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-block;
                }
                .btn-primary { background: #007bff; color: white; }
                .btn-danger { background: #dc3545; color: white; }
                .btn:hover { opacity: 0.8; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 网络分析仪表板</h1>
                    <p>实时监控和分析结果展示</p>
                </div>

                <div class="nav">
                    <a href="/">首页</a>
                    <a href="/upload">文件上传</a>
                    <a href="/dashboard">仪表板</a>
                    <a href="/docs">API文档</a>
                </div>

                <div class="content">
                    <div class="grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalAnalyses">-</div>
                            <div class="stat-label">总分析次数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalPackets">-</div>
                            <div class="stat-label">总数据包数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalFiles">-</div>
                            <div class="stat-label">上传文件数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="avgProcessTime">-</div>
                            <div class="stat-label">平均处理时间</div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>📈 最近分析结果</h3>
                        <div id="recentResults" class="loading">正在加载...</div>
                    </div>

                    <div class="card">
                        <h3>📁 上传文件管理</h3>
                        <div id="uploadedFiles" class="loading">正在加载...</div>
                    </div>
                </div>
            </div>

            <script>
                // 加载仪表板数据
                async function loadDashboardData() {
                    try {
                        // 加载分析结果
                        const resultsResponse = await fetch('/results');
                        const resultsData = await resultsResponse.json();

                        // 加载上传文件
                        const filesResponse = await fetch('/files');
                        const filesData = await filesResponse.json();

                        // 更新统计数据
                        document.getElementById('totalAnalyses').textContent = resultsData.total || 0;
                        document.getElementById('totalFiles').textContent = filesData.total || 0;

                        let totalPackets = 0;
                        resultsData.results?.forEach(result => {
                            totalPackets += result.total_packets || 0;
                        });
                        document.getElementById('totalPackets').textContent = totalPackets.toLocaleString();
                        document.getElementById('avgProcessTime').textContent = '< 1秒';

                        // 显示最近结果
                        displayRecentResults(resultsData.results || []);

                        // 显示上传文件
                        displayUploadedFiles(filesData.files || []);

                    } catch (error) {
                        console.error('加载数据失败:', error);
                        document.getElementById('recentResults').innerHTML =
                            '<div class="error">加载分析结果失败</div>';
                        document.getElementById('uploadedFiles').innerHTML =
                            '<div class="error">加载文件列表失败</div>';
                    }
                }

                function displayRecentResults(results) {
                    const container = document.getElementById('recentResults');

                    if (results.length === 0) {
                        container.innerHTML = '<p>暂无分析结果</p>';
                        return;
                    }

                    const table = `
                        <table>
                            <thead>
                                <tr>
                                    <th>分析ID</th>
                                    <th>时间戳</th>
                                    <th>数据包数</th>
                                    <th>文件大小</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${results.slice(0, 10).map(result => `
                                    <tr>
                                        <td>${result.analysis_id}</td>
                                        <td>${new Date(result.created_at).toLocaleString()}</td>
                                        <td>${result.total_packets.toLocaleString()}</td>
                                        <td>${(result.file_size / 1024).toFixed(1)} KB</td>
                                        <td>
                                            <a href="/results/${result.analysis_id}" class="btn btn-primary">查看详情</a>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;

                    container.innerHTML = table;
                }

                function displayUploadedFiles(files) {
                    const container = document.getElementById('uploadedFiles');

                    if (files.length === 0) {
                        container.innerHTML = '<p>暂无上传文件</p>';
                        return;
                    }

                    const table = `
                        <table>
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>大小</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${files.slice(0, 10).map(file => `
                                    <tr>
                                        <td>${file.filename}</td>
                                        <td>${(file.size / 1024).toFixed(1)} KB</td>
                                        <td>${new Date(file.upload_time).toLocaleString()}</td>
                                        <td>
                                            <button class="btn btn-primary" onclick="analyzeFile('${file.file_id}')">分析</button>
                                            <button class="btn btn-danger" onclick="deleteFile('${file.file_id}')">删除</button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;

                    container.innerHTML = table;
                }

                async function deleteFile(fileId) {
                    if (!confirm('确定要删除这个文件吗？')) return;

                    try {
                        const response = await fetch(`/files/${fileId}`, { method: 'DELETE' });
                        const result = await response.json();

                        if (result.success) {
                            alert('文件删除成功');
                            loadDashboardData(); // 重新加载数据
                        } else {
                            alert('删除失败: ' + result.message);
                        }
                    } catch (error) {
                        alert('删除失败: ' + error.message);
                    }
                }

                function analyzeFile(fileId) {
                    alert('分析功能开发中...');
                }

                // 页面加载时初始化
                document.addEventListener('DOMContentLoaded', loadDashboardData);

                // 每30秒自动刷新数据
                setInterval(loadDashboardData, 30000);
            </script>
        </body>
        </html>
        """

    @app.get("/visualize/{analysis_id}", response_class=HTMLResponse)
    async def visualize_analysis(analysis_id: str):
        """
        可视化分析结果页面
        """
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>可视化分析结果 - {analysis_id}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: #f5f5f5;
                }}
                .container {{
                    max-width: 1400px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    overflow: hidden;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px 30px;
                    text-align: center;
                }}
                .nav {{
                    background: #f8f9fa;
                    padding: 15px 30px;
                    border-bottom: 1px solid #dee2e6;
                }}
                .nav a {{
                    margin: 0 15px;
                    padding: 8px 16px;
                    background: #007bff;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                }}
                .nav a:hover {{ background: #0056b3; }}
                .content {{ padding: 30px; }}
                .chart-container {{
                    margin: 20px 0;
                    padding: 20px;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    background: white;
                }}
                .chart-title {{
                    font-size: 1.2em;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: #333;
                }}
                .loading {{
                    text-align: center;
                    padding: 40px;
                    color: #666;
                }}
                .error {{
                    color: #dc3545;
                    background: #f8d7da;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 10px 0;
                }}
                .grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin: 20px 0;
                }}
                @media (max-width: 768px) {{
                    .grid {{ grid-template-columns: 1fr; }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 可视化分析结果</h1>
                    <p>分析ID: {analysis_id}</p>
                </div>

                <div class="nav">
                    <a href="/">首页</a>
                    <a href="/dashboard">仪表板</a>
                    <a href="/results/{analysis_id}">原始数据</a>
                    <a href="/docs">API文档</a>
                </div>

                <div class="content">
                    <div class="chart-container">
                        <div class="chart-title">📈 流量时序图</div>
                        <div id="timelineChart" class="loading">正在生成时序图...</div>
                    </div>

                    <div class="grid">
                        <div class="chart-container">
                            <div class="chart-title">🥧 协议分布图</div>
                            <div id="protocolChart" class="loading">正在生成协议分布图...</div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">🌐 网络拓扑图</div>
                            <div id="topologyChart" class="loading">正在生成网络拓扑图...</div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-title">📊 综合仪表板</div>
                        <div id="dashboardChart" class="loading">正在生成综合仪表板...</div>
                    </div>
                </div>
            </div>

            <script>
                // 生成可视化图表
                async function generateCharts() {{
                    try {{
                        // 获取分析结果数据
                        const response = await fetch('/results/{analysis_id}');
                        const data = await response.json();

                        if (!data.success) {{
                            throw new Error('获取分析数据失败');
                        }}

                        const results = data.results;

                        // 生成时序图
                        generateTimelineChart(results);

                        // 生成协议分布图
                        generateProtocolChart(results);

                        // 生成网络拓扑图
                        generateTopologyChart(results);

                        // 生成综合仪表板
                        generateDashboardChart(results);

                    }} catch (error) {{
                        console.error('生成图表失败:', error);
                        showError('生成可视化图表失败: ' + error.message);
                    }}
                }}

                function generateTimelineChart(results) {{
                    const protocolStats = results.protocol_stats || {{}};

                    // 模拟时序数据
                    const timeData = [];
                    const now = new Date();
                    for (let i = 0; i < 24; i++) {{
                        const time = new Date(now.getTime() - (23 - i) * 60 * 60 * 1000);
                        timeData.push({{
                            x: time,
                            y: Math.random() * 1000 + 500
                        }});
                    }}

                    const trace = {{
                        x: timeData.map(d => d.x),
                        y: timeData.map(d => d.y),
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: '数据包数量',
                        line: {{ color: '#007bff' }}
                    }};

                    const layout = {{
                        title: '24小时流量趋势',
                        xaxis: {{ title: '时间' }},
                        yaxis: {{ title: '数据包数量' }},
                        margin: {{ t: 50, r: 50, b: 50, l: 50 }}
                    }};

                    Plotly.newPlot('timelineChart', [trace], layout, {{responsive: true}});
                }}

                function generateProtocolChart(results) {{
                    const protocolStats = results.protocol_stats || {{}};
                    const protocolCounts = protocolStats.protocol_counts || {{}};

                    if (Object.keys(protocolCounts).length === 0) {{
                        document.getElementById('protocolChart').innerHTML = '<p>暂无协议数据</p>';
                        return;
                    }}

                    const trace = {{
                        labels: Object.keys(protocolCounts).map(p => p.toUpperCase()),
                        values: Object.values(protocolCounts),
                        type: 'pie',
                        hole: 0.3,
                        textinfo: 'label+percent',
                        textposition: 'auto',
                        marker: {{
                            colors: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
                        }}
                    }};

                    const layout = {{
                        title: '协议分布',
                        margin: {{ t: 50, r: 50, b: 50, l: 50 }}
                    }};

                    Plotly.newPlot('protocolChart', [trace], layout, {{responsive: true}});
                }}

                function generateTopologyChart(results) {{
                    // 模拟网络拓扑数据
                    const nodes = [
                        {{ x: 0, y: 0, text: '192.168.1.1', size: 20 }},
                        {{ x: 1, y: 1, text: '192.168.1.100', size: 15 }},
                        {{ x: -1, y: 1, text: '10.0.0.1', size: 18 }},
                        {{ x: 0, y: 2, text: '8.8.8.8', size: 12 }},
                        {{ x: 2, y: 0, text: '192.168.1.200', size: 10 }}
                    ];

                    const edges = [
                        {{ x0: 0, y0: 0, x1: 1, y1: 1 }},
                        {{ x0: 0, y0: 0, x1: -1, y1: 1 }},
                        {{ x0: 1, y1: 1, x1: 0, y1: 2 }},
                        {{ x0: 0, y0: 0, x1: 2, y1: 0 }}
                    ];

                    // 绘制边
                    const edgeTrace = {{
                        x: edges.flatMap(e => [e.x0, e.x1, null]),
                        y: edges.flatMap(e => [e.y0, e.y1, null]),
                        mode: 'lines',
                        line: {{ color: '#888', width: 2 }},
                        hoverinfo: 'none',
                        showlegend: false
                    }};

                    // 绘制节点
                    const nodeTrace = {{
                        x: nodes.map(n => n.x),
                        y: nodes.map(n => n.y),
                        mode: 'markers+text',
                        marker: {{
                            size: nodes.map(n => n.size),
                            color: '#007bff',
                            line: {{ color: 'white', width: 2 }}
                        }},
                        text: nodes.map(n => n.text),
                        textposition: 'middle center',
                        textfont: {{ color: 'white', size: 10 }},
                        hoverinfo: 'text',
                        showlegend: false
                    }};

                    const layout = {{
                        title: '网络拓扑图',
                        showlegend: false,
                        xaxis: {{ showgrid: false, zeroline: false, showticklabels: false }},
                        yaxis: {{ showgrid: false, zeroline: false, showticklabels: false }},
                        margin: {{ t: 50, r: 50, b: 50, l: 50 }}
                    }};

                    Plotly.newPlot('topologyChart', [edgeTrace, nodeTrace], layout, {{responsive: true}});
                }}

                function generateDashboardChart(results) {{
                    const protocolStats = results.protocol_stats || {{}};

                    // 创建仪表板指标
                    const metrics = [
                        {{ title: '总数据包', value: protocolStats.total_packets || 0, color: '#007bff' }},
                        {{ title: '总字节数', value: protocolStats.total_bytes || 0, color: '#28a745' }},
                        {{ title: '唯一流', value: protocolStats.unique_flows || 0, color: '#ffc107' }},
                        {{ title: '协议种类', value: Object.keys(protocolStats.protocol_counts || {{}}).length, color: '#dc3545' }}
                    ];

                    const traces = metrics.map((metric, index) => ({{
                        x: [metric.title],
                        y: [metric.value],
                        type: 'bar',
                        name: metric.title,
                        marker: {{ color: metric.color }}
                    }}));

                    const layout = {{
                        title: '分析指标概览',
                        xaxis: {{ title: '指标' }},
                        yaxis: {{ title: '数值' }},
                        margin: {{ t: 50, r: 50, b: 50, l: 50 }}
                    }};

                    Plotly.newPlot('dashboardChart', traces, layout, {{responsive: true}});
                }}

                function showError(message) {{
                    const containers = ['timelineChart', 'protocolChart', 'topologyChart', 'dashboardChart'];
                    containers.forEach(id => {{
                        document.getElementById(id).innerHTML = `<div class="error">${{message}}</div>`;
                    }});
                }}

                // 页面加载时生成图表
                document.addEventListener('DOMContentLoaded', generateCharts);
            </script>
        </body>
        </html>
        """

    @app.get("/api/visualizations/{analysis_id}")
    async def get_visualization_data(analysis_id: str):
        """
        获取可视化数据API
        """
        try:
            # 这里应该根据analysis_id生成实际的可视化数据
            # 目前返回模拟数据

            visualization_data = {
                "timeline": {
                    "type": "line",
                    "data": [
                        {"x": "2025-08-26T10:00:00", "y": 150},
                        {"x": "2025-08-26T11:00:00", "y": 230},
                        {"x": "2025-08-26T12:00:00", "y": 180},
                        {"x": "2025-08-26T13:00:00", "y": 320},
                        {"x": "2025-08-26T14:00:00", "y": 280}
                    ],
                    "title": "流量时序图",
                    "xAxis": "时间",
                    "yAxis": "数据包数量"
                },
                "protocol_distribution": {
                    "type": "pie",
                    "data": [
                        {"label": "TCP", "value": 67},
                        {"label": "UDP", "value": 67},
                        {"label": "ICMP", "value": 66}
                    ],
                    "title": "协议分布图"
                },
                "network_topology": {
                    "type": "network",
                    "nodes": [
                        {"id": "192.168.1.1", "label": "Gateway", "size": 20},
                        {"id": "192.168.1.100", "label": "Client", "size": 15},
                        {"id": "10.0.0.1", "label": "Server", "size": 18}
                    ],
                    "edges": [
                        {"source": "192.168.1.1", "target": "192.168.1.100", "weight": 50},
                        {"source": "192.168.1.1", "target": "10.0.0.1", "weight": 30}
                    ],
                    "title": "网络拓扑图"
                }
            }

            return {
                "success": True,
                "analysis_id": analysis_id,
                "visualizations": visualization_data,
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"生成可视化数据失败: {str(e)}"
            )

    return app


def _create_demo_packets() -> List[Packet]:
    """创建演示数据包（实际应该从PCAP文件解析）"""
    base_time = datetime.now()
    packets = []
    
    # 创建一些示例数据包
    for i in range(50):
        packets.append(Packet(
            timestamp=base_time,
            size=64 + i * 10,
            src_ip=f"192.168.1.{100 + i % 10}",
            dst_ip=f"10.0.0.{1 + i % 5}",
            src_port=12345 + i,
            dst_port=80 if i % 3 == 0 else 443,
            protocol=ProtocolType.TCP if i % 2 == 0 else ProtocolType.UDP,
            direction=PacketDirection.OUTBOUND if i % 2 == 0 else PacketDirection.INBOUND
        ))
    
    return packets


# 创建默认应用实例
if FASTAPI_AVAILABLE:
    app = create_app()
else:
    app = None
