#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置和连接管理
Database Configuration and Connection Management

提供数据库连接、会话管理和配置功能。
支持 PostgreSQL、SQLite 等多种数据库。
"""

import os
import logging
from typing import Optional, Generator, Dict, Any
from contextlib import contextmanager

from sqlalchemy import create_engine, Engine, event
from sqlalchemy.orm import sessionmaker, Session, declarative_base
from sqlalchemy.pool import StaticPool
from sqlalchemy.exc import SQLAlchemyError

from .database import Base

logger = logging.getLogger(__name__)


class DatabaseConfig:
    """
    数据库配置类
    管理数据库连接参数和配置选项
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据库配置
        
        Args:
            config: 数据库配置字典
        """
        self.config = config or {}
        
        # 从环境变量或配置获取数据库URL
        self.database_url = self._get_database_url()
        
        # 连接池配置
        self.pool_size = self.config.get('pool_size', 10)
        self.max_overflow = self.config.get('max_overflow', 20)
        self.pool_timeout = self.config.get('pool_timeout', 30)
        self.pool_recycle = self.config.get('pool_recycle', 3600)
        
        # 其他配置
        self.echo = self.config.get('echo', False)
        self.echo_pool = self.config.get('echo_pool', False)
        
        logger.info(f"数据库配置初始化完成: {self._safe_url()}")
    
    def _get_database_url(self) -> str:
        """
        获取数据库连接URL
        优先级：环境变量 > 配置文件 > 默认值
        """
        # 从环境变量获取
        url = os.getenv('DATABASE_URL')
        if url:
            return url
        
        # 从配置文件获取
        url = self.config.get('url')
        if url:
            return url
        
        # 默认使用SQLite（开发环境）
        default_url = "sqlite:///./data/netanalysis.db"
        logger.warning(f"未找到数据库配置，使用默认SQLite: {default_url}")
        return default_url
    
    def _safe_url(self) -> str:
        """返回安全的数据库URL（隐藏密码）"""
        url = self.database_url
        if '://' in url and '@' in url:
            # 隐藏密码部分
            parts = url.split('://')
            if len(parts) == 2:
                scheme = parts[0]
                rest = parts[1]
                if '@' in rest:
                    auth_part, host_part = rest.split('@', 1)
                    if ':' in auth_part:
                        user, _ = auth_part.split(':', 1)
                        return f"{scheme}://{user}:***@{host_part}"
        return url
    
    def is_sqlite(self) -> bool:
        """检查是否使用SQLite数据库"""
        return self.database_url.startswith('sqlite')
    
    def is_postgresql(self) -> bool:
        """检查是否使用PostgreSQL数据库"""
        return self.database_url.startswith('postgresql')


class DatabaseManager:
    """
    数据库管理器
    负责创建引擎、会话和管理数据库连接
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置
        """
        self.config = DatabaseConfig(config)
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        
        self._setup_engine()
        self._setup_session()
        
        logger.info("数据库管理器初始化完成")
    
    def _setup_engine(self):
        """设置数据库引擎"""
        connect_args = {}
        
        # SQLite特殊配置
        if self.config.is_sqlite():
            connect_args = {
                "check_same_thread": False,  # 允许多线程访问
            }
            # SQLite使用StaticPool
            self.engine = create_engine(
                self.config.database_url,
                connect_args=connect_args,
                poolclass=StaticPool,
                echo=self.config.echo,
                echo_pool=self.config.echo_pool,
            )
        else:
            # PostgreSQL等其他数据库
            self.engine = create_engine(
                self.config.database_url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                echo=self.config.echo,
                echo_pool=self.config.echo_pool,
            )
        
        # 添加连接事件监听器
        self._setup_event_listeners()
    
    def _setup_session(self):
        """设置会话工厂"""
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
    
    def _setup_event_listeners(self):
        """设置数据库事件监听器"""
        
        @event.listens_for(self.engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """为SQLite设置PRAGMA选项"""
            if self.config.is_sqlite():
                cursor = dbapi_connection.cursor()
                # 启用外键约束
                cursor.execute("PRAGMA foreign_keys=ON")
                # 设置WAL模式以提高并发性能
                cursor.execute("PRAGMA journal_mode=WAL")
                # 设置同步模式
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.close()
        
        @event.listens_for(self.engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出时的处理"""
            logger.debug("数据库连接已检出")
        
        @event.listens_for(self.engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入时的处理"""
            logger.debug("数据库连接已检入")
    
    def create_tables(self):
        """创建所有数据库表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except SQLAlchemyError as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    def drop_tables(self):
        """删除所有数据库表（谨慎使用）"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("数据库表已删除")
        except SQLAlchemyError as e:
            logger.error(f"删除数据库表失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """
        获取数据库会话
        
        Returns:
            Session: 数据库会话对象
        """
        if not self.SessionLocal:
            raise RuntimeError("数据库会话工厂未初始化")
        
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """
        会话上下文管理器
        自动处理事务提交和回滚
        
        Yields:
            Session: 数据库会话对象
        """
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库事务回滚: {e}")
            raise
        finally:
            session.close()
    
    def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            Dict[str, Any]: 健康检查结果
        """
        try:
            with self.session_scope() as session:
                # 执行简单查询测试连接
                result = session.execute("SELECT 1")
                result.fetchone()
                
                return {
                    "status": "healthy",
                    "database_url": self.config._safe_url(),
                    "engine_pool_size": self.engine.pool.size() if hasattr(self.engine.pool, 'size') else None,
                    "engine_checked_out": self.engine.pool.checkedout() if hasattr(self.engine.pool, 'checkedout') else None,
                }
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "database_url": self.config._safe_url(),
            }
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


def get_database_manager(config: Optional[Dict[str, Any]] = None) -> DatabaseManager:
    """
    获取全局数据库管理器实例
    
    Args:
        config: 数据库配置（仅在首次调用时使用）
        
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global _db_manager
    
    if _db_manager is None:
        _db_manager = DatabaseManager(config)
    
    return _db_manager


def get_db_session() -> Generator[Session, None, None]:
    """
    FastAPI依赖注入函数
    获取数据库会话
    
    Yields:
        Session: 数据库会话对象
    """
    db_manager = get_database_manager()
    session = db_manager.get_session()
    try:
        yield session
    finally:
        session.close()


def init_database(config: Optional[Dict[str, Any]] = None):
    """
    初始化数据库
    创建表和必要的初始数据
    
    Args:
        config: 数据库配置
    """
    db_manager = get_database_manager(config)
    
    # 创建表
    db_manager.create_tables()
    
    # 可以在这里添加初始数据创建逻辑
    logger.info("数据库初始化完成")


def close_database():
    """关闭数据库连接"""
    global _db_manager
    
    if _db_manager:
        _db_manager.close()
        _db_manager = None
