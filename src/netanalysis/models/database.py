#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
Database Models Definition

定义网络数据包分析工具的所有数据库模型，包括用户、分析任务、文件上传等核心实体。
使用 SQLAlchemy 2.0 语法和 Pydantic 集成。
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum

from sqlalchemy import (
    Column, String, Integer, DateTime, Boolean, Text, JSON, 
    ForeignKey, Float, LargeBinary, Index, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func
from pydantic import BaseModel, Field, ConfigDict

# SQLAlchemy 基类
Base = declarative_base()


class TaskStatus(str, Enum):
    """分析任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消


class FileFormat(str, Enum):
    """文件格式枚举"""
    PCAP = "pcap"
    PCAPNG = "pcapng"
    CAP = "cap"
    TCPDUMP = "tcpdump"
    UNKNOWN = "unknown"


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"          # 管理员
    USER = "user"            # 普通用户
    VIEWER = "viewer"        # 只读用户


# ==================== 数据库模型 ====================

class User(Base):
    """
    用户模型
    存储系统用户信息，包括认证和授权相关数据
    """
    __tablename__ = "users"
    
    # 主键和基础信息
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    
    # 认证信息
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    role: Mapped[UserRole] = mapped_column(String(20), default=UserRole.USER, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # API 密钥（可选）
    api_key: Mapped[Optional[str]] = mapped_column(String(64), unique=True, index=True)
    api_key_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 关系
    uploaded_files = relationship("UploadedFile", back_populates="user", cascade="all, delete-orphan")
    analysis_tasks = relationship("AnalysisTask", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, role={self.role})>"


class UploadedFile(Base):
    """
    上传文件模型
    存储用户上传的网络数据包文件信息
    """
    __tablename__ = "uploaded_files"
    
    # 主键和基础信息
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    original_filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # 文件属性
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)
    file_hash: Mapped[str] = mapped_column(String(64), nullable=False, index=True)  # SHA-256
    file_format: Mapped[FileFormat] = mapped_column(String(20), nullable=False)
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # 文件元数据
    packet_count: Mapped[Optional[int]] = mapped_column(Integer)
    capture_start_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    capture_end_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    capture_duration: Mapped[Optional[float]] = mapped_column(Float)  # 秒
    
    # 状态信息
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # 时间戳
    uploaded_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 外键
    user_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("users.id"), nullable=False)
    
    # 关系
    user = relationship("User", back_populates="uploaded_files")
    analysis_tasks = relationship("AnalysisTask", back_populates="uploaded_file", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_uploaded_files_user_uploaded', 'user_id', 'uploaded_at'),
        Index('idx_uploaded_files_hash', 'file_hash'),
    )
    
    def __repr__(self):
        return f"<UploadedFile(id={self.id}, filename={self.filename}, size={self.file_size})>"


class AnalysisTask(Base):
    """
    分析任务模型
    存储网络数据包分析任务的信息和状态
    """
    __tablename__ = "analysis_tasks"
    
    # 主键和基础信息
    id: Mapped[str] = mapped_column(UUID(as_uuid=False), primary_key=True, default=lambda: str(uuid.uuid4()))
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # 任务状态
    status: Mapped[TaskStatus] = mapped_column(String(20), default=TaskStatus.PENDING, nullable=False)
    progress: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)  # 0.0 - 100.0
    
    # 分析配置
    analysis_config: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict)
    enable_ai_analysis: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    enable_visualization: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    enable_anomaly_detection: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # 结果信息
    result_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    result_file_path: Mapped[Optional[str]] = mapped_column(String(500))
    
    # 性能指标
    processing_time: Mapped[Optional[float]] = mapped_column(Float)  # 秒
    memory_usage: Mapped[Optional[int]] = mapped_column(Integer)     # MB
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # 外键
    user_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("users.id"), nullable=False)
    uploaded_file_id: Mapped[str] = mapped_column(UUID(as_uuid=False), ForeignKey("uploaded_files.id"), nullable=False)
    
    # 关系
    user = relationship("User", back_populates="analysis_tasks")
    uploaded_file = relationship("UploadedFile", back_populates="analysis_tasks")
    
    # 索引
    __table_args__ = (
        Index('idx_analysis_tasks_user_created', 'user_id', 'created_at'),
        Index('idx_analysis_tasks_status', 'status'),
        Index('idx_analysis_tasks_file', 'uploaded_file_id'),
    )
    
    def __repr__(self):
        return f"<AnalysisTask(id={self.id}, name={self.name}, status={self.status})>"


# ==================== Pydantic 模型 ====================

class UserBase(BaseModel):
    """用户基础模型"""
    model_config = ConfigDict(from_attributes=True)
    
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: str = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    role: UserRole = Field(UserRole.USER, description="用户角色")


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., min_length=8, description="密码")


class UserUpdate(BaseModel):
    """更新用户模型"""
    model_config = ConfigDict(from_attributes=True)
    
    email: Optional[str] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: Optional[bool] = Field(None, description="是否激活")
    role: Optional[UserRole] = Field(None, description="用户角色")


class UserResponse(UserBase):
    """用户响应模型"""
    id: str = Field(..., description="用户ID")
    is_active: bool = Field(..., description="是否激活")
    is_verified: bool = Field(..., description="是否验证")
    created_at: datetime = Field(..., description="创建时间")
    last_login: Optional[datetime] = Field(None, description="最后登录时间")


class UploadedFileBase(BaseModel):
    """上传文件基础模型"""
    model_config = ConfigDict(from_attributes=True)
    
    filename: str = Field(..., description="文件名")
    original_filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., gt=0, description="文件大小（字节）")
    file_format: FileFormat = Field(..., description="文件格式")


class UploadedFileResponse(UploadedFileBase):
    """上传文件响应模型"""
    id: str = Field(..., description="文件ID")
    file_hash: str = Field(..., description="文件哈希值")
    mime_type: str = Field(..., description="MIME类型")
    packet_count: Optional[int] = Field(None, description="数据包数量")
    capture_start_time: Optional[datetime] = Field(None, description="捕获开始时间")
    capture_end_time: Optional[datetime] = Field(None, description="捕获结束时间")
    capture_duration: Optional[float] = Field(None, description="捕获持续时间（秒）")
    is_processed: bool = Field(..., description="是否已处理")
    uploaded_at: datetime = Field(..., description="上传时间")
    processed_at: Optional[datetime] = Field(None, description="处理时间")


class AnalysisTaskBase(BaseModel):
    """分析任务基础模型"""
    model_config = ConfigDict(from_attributes=True)
    
    name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    enable_ai_analysis: bool = Field(False, description="启用AI分析")
    enable_visualization: bool = Field(True, description="启用可视化")
    enable_anomaly_detection: bool = Field(True, description="启用异常检测")


class AnalysisTaskCreate(AnalysisTaskBase):
    """创建分析任务模型"""
    uploaded_file_id: str = Field(..., description="上传文件ID")
    analysis_config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="分析配置")


class AnalysisTaskResponse(AnalysisTaskBase):
    """分析任务响应模型"""
    id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    progress: float = Field(..., ge=0, le=100, description="进度百分比")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    memory_usage: Optional[int] = Field(None, description="内存使用（MB）")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    uploaded_file_id: str = Field(..., description="上传文件ID")
