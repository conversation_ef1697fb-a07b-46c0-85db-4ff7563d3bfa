#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型模块
Database Models Module

包含网络数据包分析工具的所有数据库模型和配置。
"""

from .database import (
    # SQLAlchemy 模型
    Base,
    User,
    UploadedFile,
    AnalysisTask,

    # 枚举类型
    TaskStatus,
    FileFormat,
    UserRole,

    # Pydantic 模型
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UploadedFileBase,
    UploadedFileResponse,
    AnalysisTaskBase,
    AnalysisTaskCreate,
    AnalysisTaskResponse,
)

from .database_config import (
    DatabaseConfig,
    DatabaseManager,
    get_database_manager,
    get_db_session,
    init_database,
    close_database,
)

__all__ = [
    # SQLAlchemy 模型
    "Base",
    "User",
    "UploadedFile",
    "AnalysisTask",

    # 枚举类型
    "TaskStatus",
    "FileFormat",
    "UserRole",

    # Pydantic 模型
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UploadedFileBase",
    "UploadedFileResponse",
    "AnalysisTaskBase",
    "AnalysisTaskCreate",
    "AnalysisTaskResponse",

    # 数据库配置和管理
    "DatabaseConfig",
    "DatabaseManager",
    "get_database_manager",
    "get_db_session",
    "init_database",
    "close_database",
]